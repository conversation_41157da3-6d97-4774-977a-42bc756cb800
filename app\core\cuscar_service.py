from sqlalchemy.orm import Session
from typing import List, Optional
from app.models.cuscar import CuscarMessage
from app.schemas.cuscar_crud import CuscarMessageCreate, CuscarMessageUpdate
from loguru import logger

def get_cuscar_message(db: Session, message_id: int) -> Optional[CuscarMessage]:
    """Get a CUSCAR message by ID"""
    return db.query(CuscarMessage).filter(CuscarMessage.id == message_id, CuscarMessage.is_active == True).first()

def get_cuscar_messages(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    reference_number: Optional[str] = None
) -> List[CuscarMessage]:
    """Get all CUSCAR messages with optional filtering"""
    query = db.query(CuscarMessage).filter(CuscarMessage.is_active == True)
    
    # Apply filters if provided
    if reference_number:
        query = query.filter(CuscarMessage.reference_number == reference_number)
    
    # Get total count before pagination
    total = query.count()
    
    # Apply pagination
    messages = query.offset(skip).limit(limit).all()
    
    return messages, total

def create_cuscar_message(db: Session, message: CuscarMessageCreate) -> CuscarMessage:
    """Create a new CUSCAR message"""
    db_message = CuscarMessage(
        reference_number=message.reference_number,
        message_content=message.message_content,
        message_type=message.message_type,
        edifact_version=message.edifact_version,
        vessel_name=message.vessel_name,
        vessel_imo=message.vessel_imo,
        voyage_number=message.voyage_number,
        loading_port=message.loading_port,
        discharge_port=message.discharge_port,
        departure_date=message.departure_date,
        arrival_date=message.arrival_date
    )
    db.add(db_message)
    db.commit()
    db.refresh(db_message)
    return db_message

def update_cuscar_message(db: Session, message_id: int, message: CuscarMessageUpdate) -> Optional[CuscarMessage]:
    """Update a CUSCAR message"""
    db_message = get_cuscar_message(db, message_id)
    if not db_message:
        return None
    
    # Update fields if provided
    update_data = message.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_message, key, value)
    
    db.commit()
    db.refresh(db_message)
    return db_message

def delete_cuscar_message(db: Session, message_id: int) -> bool:
    """Soft delete a CUSCAR message"""
    db_message = get_cuscar_message(db, message_id)
    if not db_message:
        return False
    
    db_message.is_active = False
    db.commit()
    return True
