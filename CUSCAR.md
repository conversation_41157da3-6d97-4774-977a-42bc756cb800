Create a complete Python microservice application that exposes an HTTP endpoint which, when called with appropriate cargo and vessel data, processes the information and generates a valid UN/EDIFACT CUSCAR (Customs Cargo Report) message. The application should follow best practices for microservices, including proper error handling, logging, configuration management, and testing.

## Required Functionality
1. HTTP endpoint that accepts JSON payload containing cargo manifest data
2. Data validation and transformation logic
3. UN/EDIFACT CUSCAR message generation according to D96B or later standard
4. Error handling with appropriate HTTP response codes
5. Logging system for operations and errors
6. Configurable settings (via environment variables or config files)
7. Documentation for API usage and deployment

## Implementation Steps

### Step 1: Project Structure and Setup
Create a project with the following structure:
- Directory layout (main application, tests, configuration, documentation)
- Dependencies (requirements.txt)
- Configuration management
- Environment setup instructions

### Step 2: Core UN/EDIFACT Components
Implement the core components needed for EDIFACT message creation:
- Segment classes
- Data element handling
- Message structure builders
- CUSCAR-specific segments and data elements (BGM, RFF, TDT, NAD, LOC, DTM, etc.)

### Step 3: Data Models and Validation
Create models that:
- Define the expected input data structure for cargo reporting
- Implement validation rules for vessel, cargo, container information
- Handle data type conversions
- Map input data to EDIFACT elements

### Step 4: API Implementation
Build the HTTP service with:
- API endpoint definition
- Request processing
- Response formatting
- HTTP status code handling
- Rate limiting (optional)

### Step 5: CUSCAR Message Generation
Implement the logic to:
- Transform validated cargo data into EDIFACT format
- Build the complete CUSCAR message with proper hierarchy
- Format according to EDIFACT standards
- Handle vessel details, container information, and goods items
- Validate the generated message

### Step 6: Testing Framework
Create comprehensive tests for:
- Unit tests for EDIFACT component generation
- Integration tests for the API
- Example input/output test cases for various cargo scenarios
- Test coverage reporting

### Step 7: Documentation
Provide:
- API documentation
- Input data format requirements for cargo manifests
- Example requests and responses
- Deployment instructions
- Configuration options

### Step 8: Deployment Configuration
Include:
- Dockerfile
- Docker Compose file (optional)
- Environment configuration examples
- Health check endpoint

## Important Considerations
- Ensure compliance with UN/EDIFACT D96B (or specified version) standards for CUSCAR
- Include proper error handling for all edge cases
- Implement logging for troubleshooting
- Support configuration via environment variables
- Document any limitations or assumptions
- Consider handling of both vessel and cargo information
- Support different message functions (original, change, replacement)

## When Issues Arise
When needing to fix an issue, regenerate the entire codebase with the fix implemented, maintaining all context from previous versions. Each regeneration should:
1. Incorporate all previous functionality
2. Fix the specific issue
3. Update any related components that might be affected
4. Update tests to cover the fixed functionality
5. Document the changes and reason for the fix

## Example Request/Response Format
Include examples of:
- Valid JSON input format for vessel and cargo information
- Expected EDIFACT CUSCAR output
- Error response structure

## CUSCAR Specific Requirements
Ensure the implementation handles:
- Vessel identification and voyage information
- Container details and seals
- Consignor and consignee information
- Goods item descriptions and quantities
- Dangerous goods information (when applicable)
- Port information for loading and discharge
- Supporting document references

## Troubleshooting Section
Include a section on common issues and their solutions, focusing on:
- Data validation errors for cargo manifests
- EDIFACT formatting issues
- API usage problems
- Deployment challenges
- Common CUSCAR structure problems