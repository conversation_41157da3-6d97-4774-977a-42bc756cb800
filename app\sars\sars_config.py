"""
SARS-specific configuration for CUSCAR service.

This module contains configuration settings specific to the South African Revenue Service (SARS)
requirements for CUSCAR (Customs Cargo Report) messages.
"""

from pydantic import BaseSettings, Field


class SARSConfig(BaseSettings):
    """SARS-specific configuration settings."""
    
    # SARS EDI identifiers
    sender_id: str = Field("SENDER123", description="Sender ID for SARS communications")
    recipient_id: str = Field("SARSZA", description="Recipient ID for SARS (should be SARSZA)")
    
    # EDIFACT settings
    syntax_identifier: str = Field("UNOB", description="Syntax identifier (UNOB for SARS)")
    syntax_version: str = Field("4", description="Syntax version (4 for SARS)")
    
    # CUSCAR settings
    edifact_version: str = Field("D95B", description="EDIFACT version for CUSCAR (D95B for SARS)")
    message_type: str = Field("CUSCAR", description="Message type")
    message_version: str = Field("D", description="Message version")
    message_release: str = Field("95B", description="Message release")
    controlling_agency: str = Field("UN", description="Controlling agency")
    association_code: str = Field("ZZZ01", description="Association assigned code")
    
    # SARS-specific codes
    port_of_entry_codes: dict = Field(
        default={
            "ZADUR": "Durban",
            "ZAJNB": "Johannesburg",
            "ZACPT": "Cape Town",
            "ZAELS": "East London",
            "ZAPLZ": "Port Elizabeth",
            "ZAWNS": "Lanseria",
            "ZABFN": "Bloemfontein",
            "ZAKMP": "Komatipoort",
            "ZALBV": "Lebombo",
            "ZAMRE": "Maseru Bridge",
            "ZAMRB": "Mooi River",
            "ZAVIB": "Vioolsdrift",
            "ZAPRV": "Prospecton",
            "ZACTY": "City Deep",
            "ZAPMB": "Pietermaritzburg",
            "ZAWIT": "Witbank",
            "ZAUPN": "Upington",
            "ZAGEO": "George",
            "ZAPRY": "Pretoria",
            "ZAKIM": "Kimberley",
            "ZAPOL": "Polokwane",
            "ZANCS": "Newcastle",
            "ZABEL": "Bellevue",
            "ZABMA": "Beit Bridge",
            "ZAORK": "Oshoek",
            "ZAGOL": "Golela",
            "ZAFIC": "Ficksburg",
            "ZACAL": "Caledonspoort",
            "ZAVAN": "Van Rooyenshek",
            "ZAQAT": "Qacha's Nek",
            "ZANOK": "Nakop",
            "ZAROM": "Ramatlabama",
            "ZAPLA": "Plumtree",
            "ZAGRB": "Groblers Bridge",
            "ZAJEP": "Jeppe's Reef",
            "ZAKOK": "Kosi Bay",
            "ZANMA": "Nerston",
            "ZASSH": "Skilpadshek",
            "ZATWO": "Twee Rivieren",
            "ZARUN": "Richards Bay",
        },
        description="SARS port of entry codes"
    )
    
    # Transport mode codes
    transport_mode_codes: dict = Field(
        default={
            "1": "Maritime",
            "2": "Rail",
            "3": "Road",
            "4": "Air",
            "7": "Fixed transport installations",
            "8": "Inland waterway",
            "9": "Not applicable"
        },
        description="SARS transport mode codes"
    )
    
    # Package type codes
    package_type_codes: dict = Field(
        default={
            "BG": "Bag",
            "BL": "Bale",
            "BX": "Box",
            "CA": "Can",
            "CT": "Carton",
            "CS": "Case",
            "CL": "Coil",
            "CR": "Crate",
            "DR": "Drum",
            "PK": "Package",
            "PC": "Piece",
            "PL": "Pallet",
            "RO": "Roll",
            "TB": "Tub",
            "TK": "Tank",
            "UN": "Unit",
            "VG": "Bulk, gas",
            "VL": "Bulk, liquid",
            "VO": "Bulk, solid",
            "VP": "Vacuum-packed"
        },
        description="SARS package type codes"
    )
    
    class Config:
        """Pydantic configuration."""
        env_prefix = "SARS_"
        env_file = ".env"


# Create a global instance of the SARS configuration
sars_config = SARSConfig()
