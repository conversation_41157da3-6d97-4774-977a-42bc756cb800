# SARS CUSCAR Implementation

This document provides a detailed overview of the SARS-specific implementation of the CUSCAR service.

## Overview

The CUSCAR service has been implemented according to South African Revenue Service (SARS) guidelines as outlined in the following documents:

1. **Customs-G016-EDI-User-Manual-June-2016-External-Guide.pdf**
   - Contains detailed specifications for CUSCAR message structure
   - Defines SARS-specific requirements for EDI communications
   - Provides data mapping guides and branching diagrams

2. **ISO 9735-3.pdf**
   - Provides the EDIFACT syntax rules for interactive EDI
   - Defines the structure of EDIFACT messages

3. **SC-CF-55-Goods-Declaration-External-Policy.pdf**
   - Outlines the policy for goods declarations
   - Defines requirements for supporting documents
   - Specifies procedures for imports, exports, and transfers

## Implementation Details

### Configuration Settings

The following configuration settings have been added for SARS-specific requirements:

```python
class SARSConfig(BaseSettings):
    """SARS-specific configuration settings."""
    
    # SARS EDI identifiers
    sender_id: str = Field("SENDER123", description="Sender ID for SARS communications")
    recipient_id: str = Field("SARSZA", description="Recipient ID for SARS (should be SARSZA)")
    
    # EDIFACT settings
    syntax_identifier: str = Field("UNOB", description="Syntax identifier (UNOB for SARS)")
    syntax_version: str = Field("4", description="Syntax version (4 for SARS)")
    
    # CUSCAR settings
    edifact_version: str = Field("D95B", description="EDIFACT version for CUSCAR (D95B for SARS)")
    message_type: str = Field("CUSCAR", description="Message type")
    message_version: str = Field("D", description="Message version")
    message_release: str = Field("95B", description="Message release")
    controlling_agency: str = Field("UN", description="Controlling agency")
    association_code: str = Field("ZZZ01", description="Association assigned code")
    
    # SARS-specific codes
    port_of_entry_codes: dict = Field(...)
    transport_mode_codes: dict = Field(...)
    package_type_codes: dict = Field(...)
```

### Data Models

The following SARS-specific data models have been added:

#### SARSVessel Model

```python
class SARSVessel(Vessel):
    """SARS-specific vessel information."""
    
    # Additional SARS-specific fields
    vessel_nationality: Optional[str] = Field(None, description="Vessel nationality (country code)")
    vessel_type: Optional[str] = Field(None, description="Vessel type code")
    
    @validator('vessel_imo')
    def validate_vessel_imo(cls, v):
        """Validate vessel IMO number format."""
        if v and not v.startswith("IMO"):
            return f"IMO{v}"
        return v
```

#### SARSPort Model

```python
class SARSPort(Port):
    """SARS-specific port information."""
    
    # Additional SARS-specific fields
    port_function: Optional[str] = Field(None, description="Port function code (e.g., 5 for place of departure)")
```

#### SARSDateTime Model

```python
class SARSDateTime(DateTime):
    """SARS-specific date/time information."""
    
    # Additional SARS-specific fields
    date_time_qualifier: Optional[str] = Field(None, description="Date/time qualifier code")
```

#### SARSParty Model

```python
class SARSParty(Party):
    """SARS-specific party information."""
    
    # Additional SARS-specific fields
    tin_number: Optional[str] = Field(None, description="SARS Trader Identification Number")
    branch_code: Optional[str] = Field(None, description="SARS Branch Code")
    customs_code: Optional[str] = Field(None, description="SARS Customs Client Code")
```

#### SARSContainer Model

```python
class SARSContainer(Container):
    """SARS-specific container information."""
    
    # Additional SARS-specific fields
    container_status: Optional[str] = Field(None, description="Container status code")
    empty_indicator: Optional[str] = Field(None, description="Empty indicator (4 for empty, 5 for full)")
```

#### SARSGoodsItem Model

```python
class SARSGoodsItem(GoodsItem):
    """SARS-specific goods item information."""
    
    # Additional SARS-specific fields
    customs_value: Optional[float] = Field(None, description="Customs value in ZAR")
    tariff_code: Optional[str] = Field(None, description="SARS Tariff Code")
    origin_country: Optional[str] = Field(None, description="Country of origin code (ISO)")
```

#### SARSCuscarRequest Model

```python
class SARSCuscarRequest(BaseModel):
    """SARS-specific CUSCAR request model."""
    
    # Message information
    message_function: str = Field(..., description="Message function code (9=original, 5=replacement, etc.)")
    reference_number: str = Field(..., description="Reference number")
    
    # Transport information
    vessel: SARSVessel = Field(..., description="Vessel information")
    loading_port: SARSPort = Field(..., description="Port of loading")
    discharge_port: SARSPort = Field(..., description="Port of discharge")
    departure_date: SARSDateTime = Field(..., description="Departure date/time")
    arrival_date: SARSDateTime = Field(..., description="Arrival date/time")
    
    # Party information
    carrier: SARSParty = Field(..., description="Carrier information")
    
    # Additional SARS-specific fields
    transport_mode: Optional[str] = Field(None, description="Mode of transport code")
    voyage_number: Optional[str] = Field(None, description="Voyage number")
    conveyance_reference: Optional[str] = Field(None, description="Conveyance reference number")
    
    # Container loads
    container_loads: List[SARSContainerLoad] = Field(default_factory=list, description="Container loads")
```

### EDIFACT Message Generation

A SARS-specific CUSCAR generator has been implemented to generate CUSCAR messages according to SARS requirements:

```python
class SARSCuscarGenerator:
    """Generator for SARS-specific CUSCAR messages."""
    
    def __init__(self, data: Dict[str, Any]):
        """Initialize the SARS CUSCAR generator."""
        # Implementation details...
    
    def build(self) -> str:
        """Build the SARS CUSCAR message."""
        # Implementation details...
    
    def _add_unb_segment(self):
        """Add UNB segment (Interchange header)."""
        # Implementation details...
    
    def _add_unh_segment(self):
        """Add UNH segment (Message header)."""
        # Implementation details...
    
    def _add_bgm_segment(self):
        """Add BGM segment (Beginning of message)."""
        # Implementation details...
    
    def _add_reference_segments(self):
        """Add RFF segments (References)."""
        # Implementation details...
    
    def _add_transport_details(self):
        """Add TDT segment (Transport details)."""
        # Implementation details...
    
    def _add_date_time_segments(self):
        """Add DTM segments (Date/time)."""
        # Implementation details...
    
    def _add_location_segments(self):
        """Add LOC segments (Locations)."""
        # Implementation details...
    
    def _add_party_segments(self):
        """Add NAD segments (Name and address)."""
        # Implementation details...
    
    def _add_container_segments(self):
        """Add container and goods item segments."""
        # Implementation details...
    
    def _add_unt_segment(self):
        """Add UNT segment (Message trailer)."""
        # Implementation details...
    
    def _add_unz_segment(self):
        """Add UNZ segment (Interchange trailer)."""
        # Implementation details...
```

### API Enhancements

The API has been enhanced to support SARS-specific CUSCAR generation:

```python
@router.post(
    "/sars/cuscar",
    response_model=CuscarResponse,
    responses={
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse},
    },
    summary="Generate SARS CUSCAR message",
    description="Generate a SARS-specific UN/EDIFACT CUSCAR message from cargo manifest data",
)
async def generate_sars_cuscar(request: dict):
    """Generate a SARS-specific CUSCAR message from the provided cargo manifest data."""
    # Implementation details...
```

## SARS CUSCAR Message Structure

The SARS CUSCAR message follows the UN/EDIFACT D95B standard with SARS-specific customizations:

### Header Segments

- **UNB**: Interchange header
  - Contains sender and recipient IDs (SENDER123 and SARSZA)
  - Contains date and time of preparation
  - Contains interchange reference number

- **UNH**: Message header
  - Contains message reference number
  - Identifies the message as CUSCAR with version D95B

- **BGM**: Beginning of message
  - Contains document name code (785 for Cargo manifest)
  - Contains document number
  - Contains message function code (9 for original)

### Reference Segments

- **RFF**: References
  - VON qualifier for voyage number
  - CN qualifier for conveyance reference

### Transport Segments

- **TDT**: Transport details
  - Contains transport stage qualifier (20 for main carriage transport)
  - Contains transport mode (1 for maritime, 4 for air, etc.)
  - Contains vessel identification (IMO number, name, call sign)
  - Contains vessel nationality

### Date/Time Segments

- **DTM**: Date/time
  - 133 qualifier for departure date/time
  - 132 qualifier for arrival date/time
  - Contains date and time in format YYYYMMDDHHMM

### Location Segments

- **LOC**: Locations
  - 5 qualifier for place of departure
  - 7 qualifier for place of destination
  - Contains port codes

### Party Segments

- **NAD**: Name and address
  - CA qualifier for carrier
  - Contains party ID and name

### Container Segments

- **EQD**: Equipment details
  - CN qualifier for container
  - Contains container number
  - Contains container type
  - Contains container status

- **SEL**: Seal information
  - Contains seal numbers
  - Contains seal issuer

### Goods Item Segments

- **GID**: Goods item details
  - Contains goods item number
  - Contains number of packages
  - Contains package type

- **FTX**: Free text
  - AAA qualifier for goods description
  - Contains goods description

- **MEA**: Measurements
  - WT qualifier for weight
  - VOL qualifier for volume
  - Contains gross weight, net weight, and volume

- **SGP**: Split goods placement
  - Contains container number
  - Contains number of packages

- **LOC**: Location
  - 27 qualifier for country of origin
  - Contains country code

- **PIA**: Product identification
  - 5 qualifier for product identification
  - Contains tariff code

### Trailer Segments

- **UNT**: Message trailer
  - Contains segment count
  - Contains message reference number (same as UNH)

- **UNZ**: Interchange trailer
  - Contains message count
  - Contains interchange reference number (same as UNB)

## SARS-Specific Codes

### Port of Entry Codes

SARS uses specific port of entry codes for South African ports:

- ZADUR: Durban
- ZAJNB: Johannesburg
- ZACPT: Cape Town
- ZAELS: East London
- ZAPLZ: Port Elizabeth
- ZAWNS: Lanseria
- ZABFN: Bloemfontein
- ZAKMP: Komatipoort
- ZALBV: Lebombo
- ZAMRE: Maseru Bridge
- etc.

### Transport Mode Codes

SARS uses the following transport mode codes:

- 1: Maritime
- 2: Rail
- 3: Road
- 4: Air
- 7: Fixed transport installations
- 8: Inland waterway
- 9: Not applicable

### Package Type Codes

SARS uses standard package type codes:

- BG: Bag
- BL: Bale
- BX: Box
- CA: Can
- CT: Carton
- CS: Case
- CL: Coil
- CR: Crate
- DR: Drum
- PK: Package
- etc.

## Example Usage

A sample SARS CUSCAR request is provided in the `examples/sars_cuscar_request.json` file. This request can be used to generate a SARS-compliant CUSCAR message using the `/sars/cuscar` endpoint.

## Validation Rules

The implementation includes validation for SARS-specific fields:

- **Vessel IMO Number**: Must be prefixed with "IMO"
- **Container Number**: Must be 11 characters and follow ISO 6346 format
- **TIN Number**: Must be a 10-digit number
- **Branch Code**: Must be a 3-digit number
- **Customs Code**: Must be between 5 and 10 characters
- **Tariff Code**: Must be at least 8 characters
- **Country Code**: Must be 2 characters (ISO 3166-1 alpha-2)
- **Transport Mode**: Must be one of the valid transport mode codes
- **Message Function**: Must be one of the valid message function codes
- **Date Format**: Must be in YYYYMMDD format

## Conclusion

The CUSCAR service has been successfully implemented according to SARS guidelines. The implementation includes all the required SARS-specific fields, validation rules, and EDIFACT segments as outlined in the SARS documentation.
