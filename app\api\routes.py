from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from loguru import logger
from typing import Optional, List

from app.schemas.cuscar import CuscarRequest, CuscarResponse, ErrorResponse
from app.schemas.cuscar_crud import (
    CuscarMessageCreate, CuscarMessageUpdate,
    CuscarMessageInDB, CuscarMessageList
)
from app.core.cuscar_generator import generate_cuscar_message
from app.core.sars_cuscar_generator import generate_sars_cuscar_message
from app.core.cuscar_service import (
    get_cuscar_message, get_cuscar_messages,
    create_cuscar_message, update_cuscar_message, delete_cuscar_message
)
from app.models.sars_cuscar import SARSCuscarRequest
from app.models.database import get_db, Base, engine

# Create tables
Base.metadata.create_all(bind=engine)

router = APIRouter(tags=["cuscar"])

@router.post(
    "/cuscar",
    response_model=CuscarResponse,
    responses={
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse},
    },
    summary="Generate CUSCAR message",
    description="Generate a UN/EDIFACT CUSCAR message from cargo manifest data",
)
async def generate_cuscar(request: CuscarRequest):
    """
    Generate a CUSCAR (Customs Cargo Report) message from the provided cargo manifest data.

    The endpoint accepts vessel information, cargo details, and other required data
    to generate a valid UN/EDIFACT CUSCAR message according to the D96B standard.
    """
    try:
        logger.info(f"Received CUSCAR generation request for vessel: {request.vessel.vessel_name}")

        # Generate the CUSCAR message
        cuscar_message = generate_cuscar_message(request)

        logger.info(f"Successfully generated CUSCAR message for vessel: {request.vessel.vessel_name}")

        return CuscarResponse(
            message=cuscar_message,
            message_type="CUSCAR",
            edifact_version="D96B",
            status="success"
        )

    except ValueError as e:
        logger.error(f"Validation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error generating CUSCAR message: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error generating CUSCAR message"
        )

@router.post(
    "/sars/cuscar",
    response_model=CuscarResponse,
    responses={
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse},
    },
    summary="Generate SARS CUSCAR message",
    description="Generate a SARS-specific UN/EDIFACT CUSCAR message from cargo manifest data",
)
async def generate_sars_cuscar(request: dict):
    """
    Generate a SARS-specific CUSCAR (Customs Cargo Report) message from the provided cargo manifest data.

    The endpoint accepts vessel information, cargo details, and other required data
    to generate a valid UN/EDIFACT CUSCAR message according to SARS requirements.
    """
    try:
        # Validate the request data against the SARS CUSCAR model
        sars_request = SARSCuscarRequest(**request)
        logger.info(f"Received SARS CUSCAR generation request for vessel: {sars_request.vessel.vessel_name}")

        # Generate the SARS CUSCAR message
        cuscar_message = generate_sars_cuscar_message(request)

        logger.info(f"Successfully generated SARS CUSCAR message for vessel: {sars_request.vessel.vessel_name}")

        return CuscarResponse(
            message=cuscar_message,
            message_type="CUSCAR",
            edifact_version="D95B",  # SARS uses D95B for CUSCAR
            status="success"
        )
    except ValueError as e:
        logger.error(f"Validation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error generating SARS CUSCAR message: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error generating SARS CUSCAR message"
        )

@router.post(
    "/cuscar/save",
    response_model=CuscarMessageInDB,
    responses={
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse},
    },
    summary="Save CUSCAR message",
    description="Save a generated CUSCAR message to the database",
)
async def save_cuscar_message(
    message: CuscarMessageCreate,
    db: Session = Depends(get_db)
):
    """
    Save a CUSCAR message to the database.

    This endpoint allows storing a generated CUSCAR message for later retrieval.
    """
    try:
        logger.info(f"Saving CUSCAR message with reference: {message.reference_number}")

        # Save the message to the database
        db_message = create_cuscar_message(db, message)

        logger.info(f"Successfully saved CUSCAR message with ID: {db_message.id}")

        return db_message

    except Exception as e:
        logger.error(f"Error saving CUSCAR message: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error saving CUSCAR message: {str(e)}"
        )

@router.get(
    "/cuscar",
    response_model=CuscarMessageList,
    responses={
        500: {"model": ErrorResponse},
    },
    summary="List CUSCAR messages",
    description="Retrieve a list of CUSCAR messages",
)
async def list_cuscar_messages(
    skip: int = 0,
    limit: int = 100,
    reference_number: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Retrieve a list of CUSCAR messages with optional filtering.

    This endpoint returns a paginated list of CUSCAR messages stored in the database.
    """
    try:
        logger.info(f"Retrieving CUSCAR messages (skip={skip}, limit={limit})")

        # Get messages from the database
        messages, total = get_cuscar_messages(
            db, skip=skip, limit=limit, reference_number=reference_number
        )

        logger.info(f"Retrieved {len(messages)} CUSCAR messages")

        return CuscarMessageList(
            items=messages,
            total=total
        )

    except Exception as e:
        logger.error(f"Error retrieving CUSCAR messages: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving CUSCAR messages: {str(e)}"
        )

@router.get(
    "/cuscar/{message_id}",
    response_model=CuscarMessageInDB,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse},
    },
    summary="Get CUSCAR message",
    description="Retrieve a specific CUSCAR message by ID",
)
async def get_cuscar_message_by_id(
    message_id: int,
    db: Session = Depends(get_db)
):
    """
    Retrieve a specific CUSCAR message by its ID.

    This endpoint returns a single CUSCAR message from the database.
    """
    try:
        logger.info(f"Retrieving CUSCAR message with ID: {message_id}")

        # Get message from the database
        message = get_cuscar_message(db, message_id)

        if not message:
            logger.error(f"CUSCAR message with ID {message_id} not found")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"CUSCAR message with ID {message_id} not found"
            )

        logger.info(f"Retrieved CUSCAR message with ID: {message_id}")

        return message

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving CUSCAR message: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving CUSCAR message: {str(e)}"
        )

@router.put(
    "/cuscar/{message_id}",
    response_model=CuscarMessageInDB,
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse},
    },
    summary="Update CUSCAR message",
    description="Update a specific CUSCAR message by ID",
)
async def update_cuscar_message_by_id(
    message_id: int,
    message: CuscarMessageUpdate,
    db: Session = Depends(get_db)
):
    """
    Update a specific CUSCAR message by its ID.

    This endpoint allows updating the details of an existing CUSCAR message.
    """
    try:
        logger.info(f"Updating CUSCAR message with ID: {message_id}")

        # Update message in the database
        updated_message = update_cuscar_message(db, message_id, message)

        if not updated_message:
            logger.error(f"CUSCAR message with ID {message_id} not found")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"CUSCAR message with ID {message_id} not found"
            )

        logger.info(f"Updated CUSCAR message with ID: {message_id}")

        return updated_message

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating CUSCAR message: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating CUSCAR message: {str(e)}"
        )

@router.delete(
    "/cuscar/{message_id}",
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse},
    },
    summary="Delete CUSCAR message",
    description="Delete a specific CUSCAR message by ID",
)
async def delete_cuscar_message_by_id(
    message_id: int,
    db: Session = Depends(get_db)
):
    """
    Delete a specific CUSCAR message by its ID.

    This endpoint performs a soft delete of a CUSCAR message.
    """
    try:
        logger.info(f"Deleting CUSCAR message with ID: {message_id}")

        # Delete message from the database
        success = delete_cuscar_message(db, message_id)

        if not success:
            logger.error(f"CUSCAR message with ID {message_id} not found")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"CUSCAR message with ID {message_id} not found"
            )

        logger.info(f"Deleted CUSCAR message with ID: {message_id}")

        return {"status": "success", "message": f"CUSCAR message with ID {message_id} deleted"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting CUSCAR message: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting CUSCAR message: {str(e)}"
        )
