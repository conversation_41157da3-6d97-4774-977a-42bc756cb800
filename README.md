# CUSCAR Message Generator

A microservice application that generates UN/EDIFACT CUSCAR (Customs Cargo Report) messages from cargo manifest data.

## Features

- HTTP endpoint for generating CUSCAR messages
- Support for UN/EDIFACT D96B standard
- **SARS-specific implementation** for South African Revenue Service requirements
- Validation of input data
- Comprehensive error handling
- Configurable settings
- Detailed logging

## Installation

### Prerequisites

- Python 3.8+
- pip

### Setup

1. Clone the repository:
   ```
   git clone <repository-url>
   cd cuscar-generator
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Configure environment variables (optional):
   - Create a `.env` file in the root directory
   - Set variables as needed (see Configuration section)

## Usage

### Running the Application

Start the application with:

```
uvicorn app.main:app --reload
```

The API will be available at http://localhost:8000

### API Endpoints

#### Generate CUSCAR Message

```
POST /api/v1/cuscar
```

Request body should contain cargo manifest data according to the schema defined in the API documentation.

Example request:

```json
{
  "message_function": "9",
  "reference_number": "REF12345",
  "vessel": {
    "vessel_name": "TEST VESSEL",
    "vessel_imo": "IMO1234567",
    "call_sign": "ABCD",
    "voyage_number": "VOY123"
  },
  "loading_port": {
    "port_code": "USNYC",
    "port_name": "New York"
  },
  "discharge_port": {
    "port_code": "NLRTM",
    "port_name": "Rotterdam"
  },
  "departure_date": {
    "date": "20250101",
    "time": "1200"
  },
  "arrival_date": {
    "date": "20250115",
    "time": "1400"
  },
  "carrier": {
    "party_name": "TEST CARRIER",
    "party_identifier": "CARRIER123",
    "party_function": "CA"
  },
  "container_loads": [
    {
      "container": {
        "container_number": "ABCU1234567",
        "container_type": "22G1",
        "seal_numbers": ["SEAL001", "SEAL002"]
      },
      "goods_items": [
        {
          "item_number": 1,
          "description": "Test Goods",
          "packages": 10,
          "package_type": "PK",
          "gross_weight": 1000.0,
          "net_weight": 950.0,
          "volume": 20.0
        }
      ]
    }
  ]
}
```

Example response:

```json
{
  "message": "UNA:+.? '\nUNB+UNOA:2+SENDER+RECIPIENT+230415:1200+CUS2304151200'\nUNH+MSG123456+CUSCAR:D:96B:UN'\nBGM+785+REF12345+9'\nRFF+VON:VOY123'\n...",
  "message_type": "CUSCAR",
  "edifact_version": "D96B",
  "status": "success"
}
```

#### Health Check

```
GET /health
```

Returns the health status of the application.

## Configuration

The application can be configured using environment variables or a `.env` file:

| Variable | Description | Default |
|----------|-------------|---------|
| ENVIRONMENT | Application environment | development |
| LOG_LEVEL | Logging level | INFO |
| EDIFACT_VERSION | EDIFACT version to use | D96B |
| UNA_SEGMENT | UNA segment format | UNA:+.? ' |

## Testing

Run tests with:

```
pytest
```

## Docker

Build the Docker image:

```
docker build -t cuscar-generator .
```

Run the container:

```
docker run -p 8000:8000 cuscar-generator
```

## CUSCAR Message Structure

The CUSCAR message follows the UN/EDIFACT D96B standard and includes:

- UNB: Interchange header
- UNH: Message header
- BGM: Beginning of message
- RFF: References
- DTM: Date/time information
- TDT: Transport details
- LOC: Location information
- NAD: Name and address
- EQD: Equipment details
- SEL: Seal information
- GID: Goods item details
- FTX: Free text
- MEA: Measurements
- SGP: Split goods placement
- UNT: Message trailer
- UNZ: Interchange trailer

## SARS Implementation

This service includes a specialized implementation for South African Revenue Service (SARS) CUSCAR messages. The SARS implementation follows the guidelines outlined in the SARS EDI User Manual and related documentation.

### SARS-Specific Features

- SARS-specific data models with additional fields required by SARS
- Support for SARS port codes, transport mode codes, and package type codes
- Validation of SARS-specific fields (TIN number, container number format, etc.)
- Generation of CUSCAR messages according to SARS D95B standard
- API endpoint for generating SARS-specific CUSCAR messages

### SARS API Endpoint

```
POST /api/v1/sars/cuscar
```

Example request for SARS CUSCAR generation:

```json
{
  "message_function": "9",
  "reference_number": "SARS20230501001",
  "vessel": {
    "vessel_name": "TEST VESSEL",
    "vessel_imo": "1234567",
    "call_sign": "ABCD",
    "voyage_number": "VOY123",
    "vessel_nationality": "ZA",
    "vessel_type": "1"
  },
  "loading_port": {
    "port_code": "ZADUR",
    "port_name": "Durban",
    "port_function": "5"
  },
  "discharge_port": {
    "port_code": "ZAJNB",
    "port_name": "Johannesburg",
    "port_function": "7"
  },
  "carrier": {
    "party_name": "TEST CARRIER",
    "party_identifier": "CARRIER123",
    "party_function": "CA",
    "tin_number": "1234567890"
  },
  "transport_mode": "1"
}
```

For more details on the SARS implementation, see [SARS_IMPLEMENTATION.md](SARS_IMPLEMENTATION.md).

## Troubleshooting

### Common Issues

1. **Validation Errors**
   - Check that all required fields are provided
   - Ensure date formats are correct (YYYYMMDD)
   - Verify container numbers are 11 characters

2. **EDIFACT Formatting Issues**
   - Check the UNA segment configuration
   - Ensure special characters are properly escaped

3. **API Connection Issues**
   - Verify the API is running
   - Check network connectivity
   - Ensure correct endpoint URL

## License

[MIT License](LICENSE)
