"""
SARS-specific CUSCAR models.

This module contains data models specifically designed for SARS (South African Revenue Service)
CUSCAR (Customs Cargo Report) messages according to the SARS EDI User Manual.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator

from app.models.cuscar import (
    Vessel, Port, DateTime, Party, Container, GoodsItem, ContainerLoad
)


class SARSVessel(Vessel):
    """SARS-specific vessel information."""
    
    # Additional SARS-specific fields
    vessel_nationality: Optional[str] = Field(None, description="Vessel nationality (country code)")
    vessel_type: Optional[str] = Field(None, description="Vessel type code")
    
    @validator('vessel_imo')
    def validate_vessel_imo(cls, v):
        """Validate vessel IMO number format."""
        if v and not v.startswith("IMO"):
            return f"IMO{v}"
        return v


class SARSPort(Port):
    """SARS-specific port information."""
    
    # Additional SARS-specific fields
    port_function: Optional[str] = Field(None, description="Port function code (e.g., 5 for place of departure)")
    
    @validator('port_code')
    def validate_port_code(cls, v):
        """Validate port code format for SARS."""
        if v and len(v) == 5 and not v.startswith("ZA"):
            # Convert international port codes to SARS format if needed
            return v
        return v


class SARSDateTime(DateTime):
    """SARS-specific date/time information."""
    
    # Additional SARS-specific fields
    date_time_qualifier: Optional[str] = Field(None, description="Date/time qualifier code")
    
    @validator('date')
    def validate_date(cls, v):
        """Validate date format for SARS (YYYYMMDD)."""
        if v and len(v) == 8:
            try:
                datetime.strptime(v, '%Y%m%d')
                return v
            except ValueError:
                raise ValueError("Date must be in YYYYMMDD format")
        raise ValueError("Date must be in YYYYMMDD format")


class SARSParty(Party):
    """SARS-specific party information."""
    
    # Additional SARS-specific fields
    tin_number: Optional[str] = Field(None, description="SARS Trader Identification Number")
    branch_code: Optional[str] = Field(None, description="SARS Branch Code")
    customs_code: Optional[str] = Field(None, description="SARS Customs Client Code")
    
    @validator('tin_number')
    def validate_tin_number(cls, v):
        """Validate SARS Trader Identification Number format."""
        if v is not None:
            # SARS TIN is typically 10 digits
            if not v.isdigit() or len(v) != 10:
                raise ValueError("SARS TIN must be a 10-digit number")
        return v
    
    @validator('branch_code')
    def validate_branch_code(cls, v):
        """Validate SARS Branch Code format."""
        if v is not None:
            # Branch code is typically 3 digits
            if not v.isdigit() or len(v) != 3:
                raise ValueError("SARS Branch Code must be a 3-digit number")
        return v
    
    @validator('customs_code')
    def validate_customs_code(cls, v):
        """Validate SARS Customs Client Code format."""
        if v is not None:
            # Customs client code format validation
            if len(v) < 5 or len(v) > 10:
                raise ValueError("SARS Customs Client Code must be between 5 and 10 characters")
        return v


class SARSContainer(Container):
    """SARS-specific container information."""
    
    # Additional SARS-specific fields
    container_status: Optional[str] = Field(None, description="Container status code")
    empty_indicator: Optional[str] = Field(None, description="Empty indicator (4 for empty, 5 for full)")
    
    @validator('container_number')
    def validate_container_number(cls, v):
        """Validate container number format for SARS."""
        if v:
            # SARS requires container numbers to be 11 characters
            if len(v) != 11:
                raise ValueError("Container number must be 11 characters")
            
            # Check if the container number follows the ISO 6346 format
            if not (v[:4].isalpha() and v[4:].isdigit()):
                raise ValueError("Container number must follow ISO 6346 format (4 letters followed by 7 digits)")
        return v


class SARSGoodsItem(GoodsItem):
    """SARS-specific goods item information."""
    
    # Additional SARS-specific fields
    customs_value: Optional[float] = Field(None, description="Customs value in ZAR")
    tariff_code: Optional[str] = Field(None, description="SARS Tariff Code")
    origin_country: Optional[str] = Field(None, description="Country of origin code (ISO)")
    
    @validator('tariff_code')
    def validate_tariff_code(cls, v):
        """Validate SARS tariff code format."""
        if v is not None:
            # SARS tariff codes typically follow a specific format
            if len(v) < 8:
                raise ValueError("SARS tariff code must be at least 8 characters")
        return v
    
    @validator('origin_country')
    def validate_origin_country(cls, v):
        """Validate country code format."""
        if v is not None:
            # Country codes should be 2 characters (ISO 3166-1 alpha-2)
            if len(v) != 2:
                raise ValueError("Country code must be 2 characters (ISO 3166-1 alpha-2)")
        return v


class SARSContainerLoad(ContainerLoad):
    """SARS-specific container load information."""
    
    # Override fields with SARS-specific versions
    container: SARSContainer = Field(..., description="Container information")
    goods_items: List[SARSGoodsItem] = Field(default_factory=list, description="Goods items in the container")


class SARSCuscarRequest(BaseModel):
    """SARS-specific CUSCAR request model."""
    
    # Message information
    message_function: str = Field(..., description="Message function code (9=original, 5=replacement, etc.)")
    reference_number: str = Field(..., description="Reference number")
    
    # Transport information
    vessel: SARSVessel = Field(..., description="Vessel information")
    loading_port: SARSPort = Field(..., description="Port of loading")
    discharge_port: SARSPort = Field(..., description="Port of discharge")
    departure_date: SARSDateTime = Field(..., description="Departure date/time")
    arrival_date: SARSDateTime = Field(..., description="Arrival date/time")
    
    # Party information
    carrier: SARSParty = Field(..., description="Carrier information")
    
    # Additional SARS-specific fields
    transport_mode: Optional[str] = Field(None, description="Mode of transport code")
    voyage_number: Optional[str] = Field(None, description="Voyage number")
    conveyance_reference: Optional[str] = Field(None, description="Conveyance reference number")
    
    # Container loads
    container_loads: List[SARSContainerLoad] = Field(default_factory=list, description="Container loads")
    
    @validator('transport_mode')
    def validate_transport_mode(cls, v):
        """Validate transport mode code."""
        if v is not None:
            valid_modes = ['1', '2', '3', '4', '7', '8', '9']
            if v not in valid_modes:
                raise ValueError(f"Transport mode must be one of {valid_modes}")
        return v
    
    @validator('message_function')
    def validate_message_function(cls, v):
        """Validate message function code."""
        valid_functions = ['9', '5', '1', '2', '3']
        if v not in valid_functions:
            raise ValueError(f"Message function must be one of {valid_functions}")
        return v
