"""
EDIFACT message classes for the CUSCAR service.

This module provides classes for building EDIFACT messages according to the UN/EDIFACT standard.
"""

from typing import List, Optional, Dict, Any, Union


class EDIFACTElement:
    """
    Represents a single EDIFACT data element.
    
    Data elements are the basic building blocks of EDIFACT messages.
    """
    
    def __init__(self, value: str = ""):
        """
        Initialize an EDIFACT data element.
        
        Args:
            value: The value of the data element
        """
        self.value = value
    
    def __str__(self) -> str:
        """
        Convert the data element to a string.
        
        Returns:
            str: The string representation of the data element
        """
        return str(self.value) if self.value is not None else ""


class EDIFACTSegment:
    """
    Represents an EDIFACT segment.
    
    A segment is a group of related data elements.
    """
    
    def __init__(self, tag: str, elements: List[Union[EDIFACTElement, List[EDIFACTElement]]] = None):
        """
        Initialize an EDIFACT segment.
        
        Args:
            tag: The segment tag (e.g., UNB, UNH, BGM)
            elements: List of data elements or composite data elements
        """
        self.tag = tag
        self.elements = elements or []
    
    def add_element(self, element: Union[EDIFACTElement, List[EDIFACTElement]]):
        """
        Add a data element to the segment.
        
        Args:
            element: The data element or composite data element to add
        """
        self.elements.append(element)
    
    def __str__(self) -> str:
        """
        Convert the segment to a string.
        
        Returns:
            str: The string representation of the segment
        """
        result = self.tag
        
        for element in self.elements:
            result += "+"
            
            if isinstance(element, list):
                # Composite data element
                result += ":".join(str(component) for component in element)
            else:
                # Simple data element
                result += str(element)
        
        return result


class EDIFACTMessage:
    """
    Represents a complete EDIFACT message.
    
    An EDIFACT message is a collection of segments.
    """
    
    def __init__(self):
        """Initialize an EDIFACT message."""
        self.segments: List[EDIFACTSegment] = []
        self.una_segment = "UNA:+.? '"
        self.syntax_identifier = "UNOA"
        self.syntax_version = "2"
        self.sender_id = ""
        self.recipient_id = ""
    
    def add_segment(self, segment: EDIFACTSegment):
        """
        Add a segment to the message.
        
        Args:
            segment: The segment to add
        """
        self.segments.append(segment)
    
    def build(self) -> str:
        """
        Build the complete EDIFACT message.
        
        Returns:
            str: The string representation of the message
        """
        result = self.una_segment + "\n"
        
        for segment in self.segments:
            result += str(segment) + "'\n"
        
        return result
