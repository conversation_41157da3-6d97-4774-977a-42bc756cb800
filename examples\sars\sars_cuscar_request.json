{"message_function": "9", "reference_number": "SARS20230501001", "vessel": {"vessel_name": "TEST VESSEL", "vessel_imo": "1234567", "call_sign": "ABCD", "voyage_number": "VOY123", "vessel_nationality": "ZA", "vessel_type": "1"}, "loading_port": {"port_code": "ZADUR", "port_name": "Durban", "port_function": "5"}, "discharge_port": {"port_code": "ZAJNB", "port_name": "Johannesburg", "port_function": "7"}, "departure_date": {"date": "20230501", "time": "1200", "date_time_qualifier": "133"}, "arrival_date": {"date": "20230515", "time": "1400", "date_time_qualifier": "132"}, "carrier": {"party_name": "TEST CARRIER", "party_identifier": "CARRIER123", "party_function": "CA", "tin_number": "1234567890", "branch_code": "001", "customs_code": "CUST12345"}, "transport_mode": "1", "voyage_number": "VOY123", "conveyance_reference": "CONV123", "container_loads": [{"container": {"container_number": "ABCU1234567", "container_type": "22G1", "seal_numbers": ["SEAL001", "SEAL002"], "container_status": "5", "empty_indicator": "5"}, "goods_items": [{"item_number": 1, "description": "Test Goods", "packages": 10, "package_type": "PK", "gross_weight": 1000.0, "net_weight": 950.0, "volume": 20.0, "customs_value": 5000.0, "tariff_code": "84713000", "origin_country": "ZA"}]}, {"container": {"container_number": "ABCU7654321", "container_type": "22G1", "seal_numbers": ["SEAL003", "SEAL004"], "container_status": "5", "empty_indicator": "5"}, "goods_items": [{"item_number": 2, "description": "More Test Goods", "packages": 20, "package_type": "BX", "gross_weight": 2000.0, "net_weight": 1900.0, "volume": 40.0, "customs_value": 10000.0, "tariff_code": "85171200", "origin_country": "ZA"}]}]}