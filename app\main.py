from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
import os

from app.api.routes import router as api_router
from app.core.config import settings

app = FastAPI(
    title="CUSCAR Message Generator",
    description="Microservice for generating UN/EDIFACT CUSCAR messages",
    version="1.0.0",
    openapi_url="/openapi.json"
)

# Set up CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix="/api/v1")

# Setup logging
logger.add(
    "logs/cuscar_api.log",
    rotation="10 MB",
    retention="1 month",
    level="INFO",
    format="{time} {level} {message}",
)

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
