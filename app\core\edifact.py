from typing import List, Optional, Dict, Any, Union
from app.core.config import settings

class DataElement:
    """Base class for EDIFACT data elements"""
    
    def __init__(self, value: str = ""):
        self.value = value
    
    def __str__(self) -> str:
        return str(self.value) if self.value is not None else ""

class CompositeDataElement:
    """Class for composite data elements that contain multiple components"""
    
    def __init__(self, components: List[DataElement] = None):
        self.components = components or []
    
    def add_component(self, component: DataElement) -> None:
        self.components.append(component)
    
    def __str__(self) -> str:
        return ":".join(str(component) for component in self.components)

class Segment:
    """Base class for EDIFACT segments"""
    
    def __init__(self, tag: str):
        self.tag = tag
        self.elements: List[Union[DataElement, CompositeDataElement]] = []
    
    def add_element(self, element: Union[DataElement, CompositeDataElement]) -> None:
        self.elements.append(element)
    
    def __str__(self) -> str:
        elements_str = "+".join(str(element) for element in self.elements)
        if elements_str:
            return f"{self.tag}+{elements_str}"
        return self.tag

class Message:
    """Class representing a complete EDIFACT message"""
    
    def __init__(self, message_type: str, version: str = settings.EDIFACT_VERSION):
        self.message_type = message_type
        self.version = version
        self.segments: List[Segment] = []
        self.una_segment = settings.UNA_SEGMENT
    
    def add_segment(self, segment: Segment) -> None:
        self.segments.append(segment)
    
    def __str__(self) -> str:
        segments_str = "\n".join(str(segment) for segment in self.segments)
        return f"{self.una_segment}\n{segments_str}'"

# Common CUSCAR segments
class UNBSegment(Segment):
    """Interchange Header segment"""
    
    def __init__(
        self,
        syntax_identifier: str,
        sender_identification: str,
        recipient_identification: str,
        date_time: str,
        interchange_control_reference: str
    ):
        super().__init__("UNB")
        
        # Syntax identifier
        syntax_id = CompositeDataElement()
        syntax_id.add_component(DataElement("UNOA"))
        syntax_id.add_component(DataElement("2"))
        self.add_element(syntax_id)
        
        # Sender identification
        self.add_element(DataElement(sender_identification))
        
        # Recipient identification
        self.add_element(DataElement(recipient_identification))
        
        # Date and time of preparation
        date_time_element = CompositeDataElement()
        date_time_element.add_component(DataElement(date_time[:6]))  # Date YYMMDD
        date_time_element.add_component(DataElement(date_time[6:]))  # Time HHMM
        self.add_element(date_time_element)
        
        # Interchange control reference
        self.add_element(DataElement(interchange_control_reference))

class UNHSegment(Segment):
    """Message Header segment"""
    
    def __init__(self, message_reference: str, message_type: str = "CUSCAR", version: str = "D", release: str = "96B", controlling_agency: str = "UN"):
        super().__init__("UNH")
        
        # Message reference number
        self.add_element(DataElement(message_reference))
        
        # Message identifier
        message_identifier = CompositeDataElement()
        message_identifier.add_component(DataElement(message_type))
        message_identifier.add_component(DataElement(version))
        message_identifier.add_component(DataElement(release))
        message_identifier.add_component(DataElement(controlling_agency))
        self.add_element(message_identifier)

class BGMSegment(Segment):
    """Beginning of Message segment"""
    
    def __init__(self, document_name_code: str, document_number: str, message_function: str):
        super().__init__("BGM")
        
        # Document/message name, coded
        self.add_element(DataElement(document_name_code))
        
        # Document/message number
        self.add_element(DataElement(document_number))
        
        # Message function, coded
        self.add_element(DataElement(message_function))

class DTMSegment(Segment):
    """Date/Time/Period segment"""
    
    def __init__(self, date_time_period: str, format_code: str = "203", qualifier: str = "137"):
        super().__init__("DTM")
        
        # Date/time/period qualifier
        date_time_element = CompositeDataElement()
        date_time_element.add_component(DataElement(qualifier))
        date_time_element.add_component(DataElement(date_time_period))
        date_time_element.add_component(DataElement(format_code))
        self.add_element(date_time_element)

class RFFSegment(Segment):
    """Reference segment"""
    
    def __init__(self, reference_qualifier: str, reference_number: str):
        super().__init__("RFF")
        
        # Reference qualifier and number
        reference = CompositeDataElement()
        reference.add_component(DataElement(reference_qualifier))
        reference.add_component(DataElement(reference_number))
        self.add_element(reference)

class NADSegment(Segment):
    """Name and Address segment"""
    
    def __init__(self, party_qualifier: str, party_id: Optional[str] = None, party_name: Optional[str] = None):
        super().__init__("NAD")
        
        # Party qualifier
        self.add_element(DataElement(party_qualifier))
        
        # Party ID
        if party_id:
            party_id_element = CompositeDataElement()
            party_id_element.add_component(DataElement(party_id))
            self.add_element(party_id_element)
        else:
            self.add_element(DataElement(""))
        
        # Party name
        if party_name:
            self.add_element(DataElement(party_name))

class TDTSegment(Segment):
    """Transport Information segment"""
    
    def __init__(self, transport_stage_code: str, mode_of_transport: str, transport_identification: str, transport_name: str):
        super().__init__("TDT")
        
        # Transport stage qualifier
        self.add_element(DataElement(transport_stage_code))
        
        # Mode of transport, coded
        self.add_element(DataElement(mode_of_transport))
        
        # Empty element for carrier
        self.add_element(DataElement(""))
        
        # Transport identification
        transport_id = CompositeDataElement()
        transport_id.add_component(DataElement(""))  # ID type code
        transport_id.add_component(DataElement(transport_identification))
        self.add_element(transport_id)
        
        # Transport name
        self.add_element(DataElement(transport_name))

class LOCSegment(Segment):
    """Location segment"""
    
    def __init__(self, location_qualifier: str, location_code: str, location_name: Optional[str] = None):
        super().__init__("LOC")
        
        # Location qualifier
        self.add_element(DataElement(location_qualifier))
        
        # Location identification
        location = CompositeDataElement()
        location.add_component(DataElement(location_code))
        location.add_component(DataElement("6"))  # 6 = UN/LOCODE
        self.add_element(location)
        
        # Location name
        if location_name:
            self.add_element(DataElement(location_name))

class EQDSegment(Segment):
    """Equipment Details segment"""
    
    def __init__(self, equipment_qualifier: str, equipment_id: str, equipment_size_type: Optional[str] = None, equipment_status: Optional[str] = None):
        super().__init__("EQD")
        
        # Equipment qualifier
        self.add_element(DataElement(equipment_qualifier))
        
        # Equipment identification
        self.add_element(DataElement(equipment_id))
        
        # Equipment size and type
        if equipment_size_type:
            equipment_type = CompositeDataElement()
            equipment_type.add_component(DataElement(equipment_size_type))
            self.add_element(equipment_type)
        
        # Equipment status
        if equipment_status:
            self.add_element(DataElement(equipment_status))

class SELSegment(Segment):
    """Seal Number segment"""
    
    def __init__(self, seal_number: str, seal_issuer: Optional[str] = None):
        super().__init__("SEL")
        
        # Seal number
        seal = CompositeDataElement()
        seal.add_component(DataElement(seal_number))
        self.add_element(seal)
        
        # Seal issuer
        if seal_issuer:
            self.add_element(DataElement(seal_issuer))

class GIDSegment(Segment):
    """Goods Item Details segment"""
    
    def __init__(self, goods_item_number: str):
        super().__init__("GID")
        
        # Goods item number
        self.add_element(DataElement(goods_item_number))

class FTXSegment(Segment):
    """Free Text segment"""
    
    def __init__(self, text_subject_code: str, text_function_code: Optional[str] = None, text_reference_code: Optional[str] = None, text: Optional[str] = None):
        super().__init__("FTX")
        
        # Text subject qualifier
        self.add_element(DataElement(text_subject_code))
        
        # Text function, coded
        if text_function_code:
            self.add_element(DataElement(text_function_code))
        else:
            self.add_element(DataElement(""))
        
        # Text reference
        if text_reference_code:
            text_ref = CompositeDataElement()
            text_ref.add_component(DataElement(text_reference_code))
            self.add_element(text_ref)
        else:
            self.add_element(DataElement(""))
        
        # Text
        if text:
            text_element = CompositeDataElement()
            text_element.add_component(DataElement(text))
            self.add_element(text_element)

class MEASegment(Segment):
    """Measurements segment"""
    
    def __init__(self, measurement_purpose: str, measurement_dimension: str, measurement_value: str, unit_code: str):
        super().__init__("MEA")
        
        # Measurement purpose qualifier
        self.add_element(DataElement(measurement_purpose))
        
        # Measurement dimension, coded
        self.add_element(DataElement(measurement_dimension))
        
        # Measurement value
        measurement = CompositeDataElement()
        measurement.add_component(DataElement(unit_code))
        measurement.add_component(DataElement(measurement_value))
        self.add_element(measurement)

class SGPSegment(Segment):
    """Split Goods Placement segment"""
    
    def __init__(self, equipment_id: str, package_count: Optional[str] = None):
        super().__init__("SGP")
        
        # Equipment identification
        self.add_element(DataElement(equipment_id))
        
        # Number of packages
        if package_count:
            self.add_element(DataElement(package_count))

class UNTSegment(Segment):
    """Message Trailer segment"""
    
    def __init__(self, segment_count: int, message_reference: str):
        super().__init__("UNT")
        
        # Number of segments in the message
        self.add_element(DataElement(str(segment_count)))
        
        # Message reference number
        self.add_element(DataElement(message_reference))

class UNZSegment(Segment):
    """Interchange Trailer segment"""
    
    def __init__(self, message_count: int, interchange_control_reference: str):
        super().__init__("UNZ")
        
        # Interchange control count
        self.add_element(DataElement(str(message_count)))
        
        # Interchange control reference
        self.add_element(DataElement(interchange_control_reference))
