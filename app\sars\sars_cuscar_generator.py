"""
SARS-specific CUSCAR message generator.

This module contains functions for generating CUSCAR (Customs Cargo Report) messages
according to SARS (South African Revenue Service) requirements.
"""

import os
from datetime import datetime
from typing import Dict, Any, List, Optional

from loguru import logger

from app.core.edifact import EDIFACTMessage, EDIFACTSegment, EDIFACTElement
from app.models.sars_cuscar import SARSCuscarRequest
from app.config.sars_config import sars_config


class SARSCuscarGenerator:
    """
    Generator for SARS-specific CUSCAR messages.
    
    This class generates UN/EDIFACT CUSCAR messages according to SARS requirements.
    """
    
    def __init__(self, data: Dict[str, Any]):
        """
        Initialize the SARS CUSCAR generator.
        
        Args:
            data: Dictionary containing the cargo manifest data
        """
        self.data = data
        self.message = EDIFACTMessage()
        
        # Set EDIFACT configuration
        self.message.una_segment = os.getenv("UNA_SEGMENT", "UNA:+.? '")
        self.message.syntax_identifier = sars_config.syntax_identifier
        self.message.syntax_version = sars_config.syntax_version
        self.message.sender_id = os.getenv("SENDER_ID", sars_config.sender_id)
        self.message.recipient_id = os.getenv("RECIPIENT_ID", sars_config.recipient_id)
        
        # Generate a unique message reference
        self.message_reference = f"MSG{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def build(self) -> str:
        """
        Build the SARS CUSCAR message.
        
        Returns:
            str: The complete EDIFACT message
        """
        logger.info("Building SARS CUSCAR message")
        
        # Add UNB segment (Interchange header)
        self._add_unb_segment()
        
        # Add UNH segment (Message header)
        self._add_unh_segment()
        
        # Add BGM segment (Beginning of message)
        self._add_bgm_segment()
        
        # Add RFF segments (References)
        self._add_reference_segments()
        
        # Add TDT segment (Transport details)
        self._add_transport_details()
        
        # Add DTM segments (Date/time)
        self._add_date_time_segments()
        
        # Add LOC segments (Locations)
        self._add_location_segments()
        
        # Add NAD segments (Name and address)
        self._add_party_segments()
        
        # Add container and goods item segments
        self._add_container_segments()
        
        # Add UNT segment (Message trailer)
        self._add_unt_segment()
        
        # Add UNZ segment (Interchange trailer)
        self._add_unz_segment()
        
        # Build the complete message
        return self.message.build()
    
    def _add_unb_segment(self):
        """Add UNB segment (Interchange header)."""
        current_datetime = datetime.now().strftime("%y%m%d:%H%M")
        interchange_reference = f"CUS{datetime.now().strftime('%y%m%d%H%M%S')}"
        
        self.message.add_segment(EDIFACTSegment("UNB", [
            [EDIFACTElement(self.message.syntax_identifier), EDIFACTElement(self.message.syntax_version)],
            [EDIFACTElement(self.message.sender_id)],
            [EDIFACTElement(self.message.recipient_id)],
            [EDIFACTElement(current_datetime)],
            EDIFACTElement(interchange_reference),
            [EDIFACTElement(self.data.get("reference_number", ""))],
            EDIFACTElement(sars_config.message_type)
        ]))
    
    def _add_unh_segment(self):
        """Add UNH segment (Message header)."""
        self.message.add_segment(EDIFACTSegment("UNH", [
            EDIFACTElement(self.message_reference),
            [
                EDIFACTElement(sars_config.message_type),
                EDIFACTElement(sars_config.message_version),
                EDIFACTElement(sars_config.message_release),
                EDIFACTElement(sars_config.controlling_agency),
                EDIFACTElement(sars_config.association_code)
            ]
        ]))
    
    def _add_bgm_segment(self):
        """Add BGM segment (Beginning of message)."""
        # For CUSCAR, the document name code is 785 (Cargo manifest)
        document_name_code = "785"
        
        self.message.add_segment(EDIFACTSegment("BGM", [
            EDIFACTElement(document_name_code),
            EDIFACTElement(self.data.get("reference_number", "")),
            EDIFACTElement(self.data.get("message_function", "9"))
        ]))
    
    def _add_reference_segments(self):
        """Add RFF segments (References)."""
        # Add voyage number reference
        voyage_number = self.data.get("voyage_number") or self.data.get("vessel", {}).get("voyage_number")
        if voyage_number:
            self.message.add_segment(EDIFACTSegment("RFF", [
                [EDIFACTElement("VON"), EDIFACTElement(voyage_number)]
            ]))
        
        # Add conveyance reference if available
        conveyance_reference = self.data.get("conveyance_reference")
        if conveyance_reference:
            self.message.add_segment(EDIFACTSegment("RFF", [
                [EDIFACTElement("CN"), EDIFACTElement(conveyance_reference)]
            ]))
    
    def _add_transport_details(self):
        """Add TDT segment (Transport details)."""
        vessel_data = self.data.get("vessel", {})
        
        # Transport stage qualifier (20 = Main carriage transport)
        transport_stage_qualifier = "20"
        
        # Transport mode (1 = Maritime, 4 = Air, etc.)
        transport_mode = self.data.get("transport_mode", "1")
        
        self.message.add_segment(EDIFACTSegment("TDT", [
            EDIFACTElement(transport_stage_qualifier),
            EDIFACTElement(vessel_data.get("voyage_number", "")),
            EDIFACTElement(transport_mode),
            EDIFACTElement(""),  # Carrier (coded)
            [
                EDIFACTElement(""),  # ID type
                EDIFACTElement(vessel_data.get("vessel_imo", "")),  # ID
                EDIFACTElement("146")  # Code list qualifier (146 = IMO)
            ]
        ]))
        
        # Add vessel name in another TDT segment
        self.message.add_segment(EDIFACTSegment("TDT", [
            EDIFACTElement(transport_stage_qualifier),
            EDIFACTElement(vessel_data.get("voyage_number", "")),
            EDIFACTElement(""),  # Transport mode
            EDIFACTElement(""),  # Carrier (coded)
            EDIFACTElement(""),  # Carrier
            EDIFACTElement(""),  # Transit direction
            [
                EDIFACTElement("11"),  # ID type (11 = Ship's name)
                EDIFACTElement(vessel_data.get("vessel_name", "")),  # ID
                EDIFACTElement("")  # Code list qualifier
            ]
        ]))
        
        # Add call sign if available
        if vessel_data.get("call_sign"):
            self.message.add_segment(EDIFACTSegment("TDT", [
                EDIFACTElement(transport_stage_qualifier),
                EDIFACTElement(vessel_data.get("voyage_number", "")),
                EDIFACTElement(""),  # Transport mode
                EDIFACTElement(""),  # Carrier (coded)
                EDIFACTElement(""),  # Carrier
                EDIFACTElement(""),  # Transit direction
                [
                    EDIFACTElement("103"),  # ID type (103 = Call sign)
                    EDIFACTElement(vessel_data.get("call_sign", "")),  # ID
                    EDIFACTElement("")  # Code list qualifier
                ]
            ]))
        
        # Add vessel nationality if available
        if vessel_data.get("vessel_nationality"):
            self.message.add_segment(EDIFACTSegment("TDT", [
                EDIFACTElement(transport_stage_qualifier),
                EDIFACTElement(vessel_data.get("voyage_number", "")),
                EDIFACTElement(""),  # Transport mode
                EDIFACTElement(""),  # Carrier (coded)
                EDIFACTElement(""),  # Carrier
                EDIFACTElement(""),  # Transit direction
                EDIFACTElement(""),  # ID
                [
                    EDIFACTElement(vessel_data.get("vessel_nationality", "")),  # Nationality
                    EDIFACTElement("162")  # Code list qualifier (162 = ISO 3166)
                ]
            ]))
    
    def _add_date_time_segments(self):
        """Add DTM segments (Date/time)."""
        # Add departure date/time
        departure_date = self.data.get("departure_date", {})
        if departure_date:
            date_str = departure_date.get("date", "")
            time_str = departure_date.get("time", "")
            
            if date_str and time_str:
                # Format: YYYYMMDDHHMM
                date_time = f"{date_str}{time_str}"
                
                self.message.add_segment(EDIFACTSegment("DTM", [
                    [EDIFACTElement("133"), EDIFACTElement(date_time), EDIFACTElement("203")]
                ]))
        
        # Add arrival date/time
        arrival_date = self.data.get("arrival_date", {})
        if arrival_date:
            date_str = arrival_date.get("date", "")
            time_str = arrival_date.get("time", "")
            
            if date_str and time_str:
                # Format: YYYYMMDDHHMM
                date_time = f"{date_str}{time_str}"
                
                self.message.add_segment(EDIFACTSegment("DTM", [
                    [EDIFACTElement("132"), EDIFACTElement(date_time), EDIFACTElement("203")]
                ]))
    
    def _add_location_segments(self):
        """Add LOC segments (Locations)."""
        # Add loading port (5 = Place of departure)
        loading_port = self.data.get("loading_port", {})
        if loading_port:
            self.message.add_segment(EDIFACTSegment("LOC", [
                EDIFACTElement("5"),  # Location function code qualifier
                [
                    EDIFACTElement(loading_port.get("port_code", "")),  # Location ID
                    EDIFACTElement("139")  # Code list qualifier (139 = Port)
                ]
            ]))
        
        # Add discharge port (7 = Place of destination)
        discharge_port = self.data.get("discharge_port", {})
        if discharge_port:
            self.message.add_segment(EDIFACTSegment("LOC", [
                EDIFACTElement("7"),  # Location function code qualifier
                [
                    EDIFACTElement(discharge_port.get("port_code", "")),  # Location ID
                    EDIFACTElement("139")  # Code list qualifier (139 = Port)
                ]
            ]))
    
    def _add_party_segments(self):
        """Add NAD segments (Name and address)."""
        # Add carrier information
        carrier = self.data.get("carrier", {})
        if carrier:
            self.message.add_segment(EDIFACTSegment("NAD", [
                EDIFACTElement(carrier.get("party_function", "CA")),  # Party function code qualifier (CA = Carrier)
                [
                    EDIFACTElement(carrier.get("party_identifier", "")),  # Party ID
                    EDIFACTElement(""),  # Code list qualifier
                    EDIFACTElement("9")  # Code list responsible agency (9 = Custom)
                ],
                EDIFACTElement(carrier.get("party_name", ""))  # Party name
            ]))
            
            # Add TIN number if available
            tin_number = carrier.get("tin_number")
            if tin_number:
                self.message.add_segment(EDIFACTSegment("RFF", [
                    [EDIFACTElement("TN"), EDIFACTElement(tin_number)]
                ]))
            
            # Add customs code if available
            customs_code = carrier.get("customs_code")
            if customs_code:
                self.message.add_segment(EDIFACTSegment("RFF", [
                    [EDIFACTElement("ABO"), EDIFACTElement(customs_code)]
                ]))
    
    def _add_container_segments(self):
        """Add container and goods item segments."""
        container_loads = self.data.get("container_loads", [])
        
        for container_load in container_loads:
            container = container_load.get("container", {})
            goods_items = container_load.get("goods_items", [])
            
            # Add EQD segment (Equipment details)
            self.message.add_segment(EDIFACTSegment("EQD", [
                EDIFACTElement("CN"),  # Equipment type code qualifier (CN = Container)
                [
                    EDIFACTElement(container.get("container_number", "")),  # Equipment ID
                    EDIFACTElement(""),  # Code list qualifier
                    EDIFACTElement("ZZZ")  # Code list responsible agency
                ],
                [
                    EDIFACTElement(container.get("container_type", "")),  # Equipment type
                    EDIFACTElement("102")  # Code list qualifier (102 = ISO 6346)
                ],
                EDIFACTElement(""),  # Equipment size and type
                EDIFACTElement(container.get("empty_indicator", "5")),  # Equipment status (5 = Full)
                EDIFACTElement(""),  # Full/empty indicator
                EDIFACTElement(container.get("container_status", ""))  # Container status
            ]))
            
            # Add seal information
            seal_numbers = container.get("seal_numbers", [])
            for seal_number in seal_numbers:
                self.message.add_segment(EDIFACTSegment("SEL", [
                    EDIFACTElement(seal_number),  # Seal number
                    EDIFACTElement("CA")  # Seal issuer (CA = Carrier)
                ]))
            
            # Add goods items
            for i, goods_item in enumerate(goods_items, 1):
                # Add GID segment (Goods item details)
                self.message.add_segment(EDIFACTSegment("GID", [
                    EDIFACTElement(str(i)),  # Goods item number
                    EDIFACTElement(str(goods_item.get("packages", ""))),  # Number of packages
                    [
                        EDIFACTElement(goods_item.get("package_type", "")),  # Package type
                        EDIFACTElement("21")  # Code list qualifier (21 = UNECE)
                    ]
                ]))
                
                # Add FTX segment (Free text - goods description)
                self.message.add_segment(EDIFACTSegment("FTX", [
                    EDIFACTElement("AAA"),  # Text subject code qualifier (AAA = Goods description)
                    EDIFACTElement(""),  # Text function code
                    EDIFACTElement(""),  # Text reference
                    EDIFACTElement(""),  # Text literal code
                    EDIFACTElement(goods_item.get("description", ""))  # Free text
                ]))
                
                # Add MEA segment (Measurements - gross weight)
                if goods_item.get("gross_weight"):
                    self.message.add_segment(EDIFACTSegment("MEA", [
                        EDIFACTElement("WT"),  # Measurement purpose code qualifier (WT = Weight)
                        EDIFACTElement("AAB"),  # Measurement dimension code (AAB = Gross weight)
                        [
                            EDIFACTElement("KGM"),  # Unit of measure (KGM = Kilogram)
                            EDIFACTElement(str(goods_item.get("gross_weight", "")))  # Weight
                        ]
                    ]))
                
                # Add MEA segment (Measurements - net weight)
                if goods_item.get("net_weight"):
                    self.message.add_segment(EDIFACTSegment("MEA", [
                        EDIFACTElement("WT"),  # Measurement purpose code qualifier (WT = Weight)
                        EDIFACTElement("AAL"),  # Measurement dimension code (AAL = Net weight)
                        [
                            EDIFACTElement("KGM"),  # Unit of measure (KGM = Kilogram)
                            EDIFACTElement(str(goods_item.get("net_weight", "")))  # Weight
                        ]
                    ]))
                
                # Add MEA segment (Measurements - volume)
                if goods_item.get("volume"):
                    self.message.add_segment(EDIFACTSegment("MEA", [
                        EDIFACTElement("VOL"),  # Measurement purpose code qualifier (VOL = Volume)
                        EDIFACTElement("AAW"),  # Measurement dimension code (AAW = Gross volume)
                        [
                            EDIFACTElement("MTQ"),  # Unit of measure (MTQ = Cubic meter)
                            EDIFACTElement(str(goods_item.get("volume", "")))  # Volume
                        ]
                    ]))
                
                # Add SGP segment (Split goods placement)
                self.message.add_segment(EDIFACTSegment("SGP", [
                    [
                        EDIFACTElement(container.get("container_number", "")),  # Equipment ID
                        EDIFACTElement(""),  # Code list qualifier
                        EDIFACTElement("ZZZ")  # Code list responsible agency
                    ],
                    EDIFACTElement(str(goods_item.get("packages", "")))  # Number of packages
                ]))
                
                # Add country of origin if available
                origin_country = goods_item.get("origin_country")
                if origin_country:
                    self.message.add_segment(EDIFACTSegment("LOC", [
                        EDIFACTElement("27"),  # Location function code qualifier (27 = Country of origin)
                        [
                            EDIFACTElement(origin_country),  # Location ID
                            EDIFACTElement("162")  # Code list qualifier (162 = ISO 3166)
                        ]
                    ]))
                
                # Add tariff code if available
                tariff_code = goods_item.get("tariff_code")
                if tariff_code:
                    self.message.add_segment(EDIFACTSegment("PIA", [
                        EDIFACTElement("5"),  # Product id function qualifier (5 = Product identification)
                        [
                            EDIFACTElement(tariff_code),  # Item number
                            EDIFACTElement("HS"),  # Item number type (HS = Harmonized system)
                            EDIFACTElement("23")  # Code list qualifier (23 = Customs)
                        ]
                    ]))
    
    def _add_unt_segment(self):
        """Add UNT segment (Message trailer)."""
        # Number of segments in the message (including UNH and UNT)
        segment_count = len(self.message.segments) + 1  # +1 for UNT itself
        
        self.message.add_segment(EDIFACTSegment("UNT", [
            EDIFACTElement(str(segment_count)),
            EDIFACTElement(self.message_reference)
        ]))
    
    def _add_unz_segment(self):
        """Add UNZ segment (Interchange trailer)."""
        # Number of messages in the interchange
        message_count = "1"
        
        # Interchange reference (same as in UNB)
        interchange_reference = f"CUS{datetime.now().strftime('%y%m%d%H%M%S')}"
        
        self.message.add_segment(EDIFACTSegment("UNZ", [
            EDIFACTElement(message_count),
            EDIFACTElement(interchange_reference)
        ]))


def generate_sars_cuscar_message(request: Dict[str, Any]) -> str:
    """
    Generate a SARS-specific CUSCAR message from the provided data.
    
    Args:
        request: Dictionary containing the cargo manifest data
        
    Returns:
        str: The generated CUSCAR message
    """
    logger.info("Generating SARS CUSCAR message")
    
    # Validate the request data
    SARSCuscarRequest(**request)
    
    # Generate the CUSCAR message
    generator = SARSCuscarGenerator(request)
    cuscar_message = generator.build()
    
    logger.info("SARS CUSCAR message generated successfully")
    
    return cuscar_message
