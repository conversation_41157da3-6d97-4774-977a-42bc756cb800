"""
SARS-specific CUSCAR models.

This module contains data models specifically designed for SARS (South African Revenue Service)
CUSCAR (Customs Cargo Report) messages.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from datetime import datetime


class SARSCuscarVessel(BaseModel):
    """Vessel information for SARS CUSCAR."""
    
    vessel_id: str = Field(..., description="Vessel identifier (IMO number)")
    vessel_name: str = Field(..., description="Vessel name")
    voyage_number: str = Field(..., description="Voyage number")
    carrier_code: str = Field(..., description="Carrier code")
    carrier_name: Optional[str] = Field(None, description="Carrier name")
    nationality: Optional[str] = Field(None, description="Vessel nationality")


class SARSCuscarRequest(BaseModel):
    """SARS-specific CUSCAR request model."""
    
    # Message information
    message_reference: str = Field(..., description="Message reference number")
    message_function: str = Field("9", description="Message function code (9=original, 4=change, 1=cancellation)")
    message_date: str = Field(..., description="Message date (YYMMDD)")
    document_number: str = Field(..., description="Document number")
    
    # Sender information
    sender_id: str = Field(..., description="Sender identifier")
    sender_name: Optional[str] = Field(None, description="Sender name")
    
    # Vessel information
    vessel: SARSCuscarVessel = Field(..., description="Vessel information")
    
    # Port information
    port_of_loading: str = Field(..., description="Port of loading")
    port_of_discharge: str = Field(..., description="Port of discharge")
    
    # Cargo information
    # This would be expanded in a real implementation
    
    @validator('message_date')
    def validate_message_date(cls, v):
        """Validate message date format."""
        if v and len(v) != 6:
            raise ValueError("Message date must be in YYMMDD format")
        try:
            datetime.strptime(v, '%y%m%d')
            return v
        except ValueError:
            raise ValueError("Message date must be in YYMMDD format")


class SARSCuscarResponse(BaseModel):
    """SARS-specific CUSCAR response model."""
    
    message: str = Field(..., description="The generated CUSCAR message")
    status: str = Field("success", description="Status of the operation")
    message_type: str = Field("CUSCAR", description="Message type")
    edifact_version: str = Field("D95B", description="EDIFACT version")


class SARSCuscarParseResult(BaseModel):
    """SARS-specific CUSCAR parse result model."""
    
    message_reference: str = Field(..., description="Message reference number")
    message_function: str = Field(..., description="Message function code")
    document_number: str = Field(..., description="Document number")
    vessel: SARSCuscarVessel = Field(..., description="Vessel information")
    port_of_loading: str = Field(..., description="Port of loading")
    port_of_discharge: str = Field(..., description="Port of discharge")
    original_message: str = Field(..., description="Original CUSCAR message")
    status: str = Field("success", description="Status of the parsing operation")
