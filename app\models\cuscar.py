from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean
from datetime import datetime
from app.models.database import Base

class CuscarMessage(Base):
    """Database model for CUSCAR messages"""
    
    __tablename__ = "cuscar_messages"
    
    id = Column(Integer, primary_key=True, index=True)
    reference_number = Column(String(50), index=True)
    message_content = Column(Text, nullable=False)
    message_type = Column(String(20), default="CUSCAR")
    edifact_version = Column(String(10), default="D96B")
    vessel_name = Column(String(100))
    vessel_imo = Column(String(20))
    voyage_number = Column(String(20))
    loading_port = Column(String(20))
    discharge_port = Column(String(20))
    departure_date = Column(String(20))
    arrival_date = Column(String(20))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)
