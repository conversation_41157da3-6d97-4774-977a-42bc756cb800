import pytest
from fastapi.testclient import Test<PERSON>lient
from app.main import app
from tests.test_cuscar_generator import create_sample_request

client = TestClient(app)

def test_health_check():
    """Test the health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}

def test_cuscar_endpoint():
    """Test the CUSCAR generation endpoint"""
    # Create a sample request
    request_data = create_sample_request().model_dump()

    # Call the API
    response = client.post("/api/v1/cuscar", json=request_data)

    # Check the response
    assert response.status_code == 200
    response_data = response.json()
    assert response_data["status"] == "success"
    assert response_data["message_type"] == "CUSCAR"
    assert response_data["edifact_version"] == "D96B"
    assert "message" in response_data
    assert len(response_data["message"]) > 0

def test_cuscar_endpoint_invalid_data():
    """Test the CUSCAR generation endpoint with invalid data"""
    # Create an invalid request (missing required fields)
    invalid_request = {
        "message_function": "9",
        "reference_number": "REF12345"
        # Missing other required fields
    }

    # Call the API
    response = client.post("/api/v1/cuscar", json=invalid_request)

    # Check the response
    assert response.status_code == 422  # Validation error
