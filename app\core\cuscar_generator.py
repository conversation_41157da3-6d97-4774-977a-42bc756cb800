from app.schemas.cuscar import CuscarRequest
from app.core.edifact import (
    Message, UNBSegment, UNHSegment, BGMSegment, DTMSegment, RFFSegment,
    NADSegment, TDTSegment, LOCSegment, EQDSegment, SELSegment, GIDSegment,
    FTXSegment, MEASegment, SGPSegment, UNTSegment, UNZSegment, DataElement
)
from datetime import datetime
import uuid
from loguru import logger

def generate_cuscar_message(request: CuscarRequest) -> str:
    """
    Generate a CUSCAR message from the provided request data
    
    Args:
        request: The CUSCAR request containing cargo manifest data
        
    Returns:
        str: The formatted EDIFACT CUSCAR message
    """
    try:
        # Create a new CUSCAR message
        cuscar = Message("CUSCAR")
        
        # Generate a unique reference for this interchange
        interchange_ref = f"CUS{datetime.now().strftime('%y%m%d%H%M')}"
        message_ref = f"MSG{uuid.uuid4().hex[:6].upper()}"
        
        # Add UNB segment (Interchange Header)
        current_datetime = datetime.now().strftime("%y%m%d%H%M")
        unb = UNBSegment(
            syntax_identifier="UNOA:2",
            sender_identification="SENDER",
            recipient_identification="RECIPIENT",
            date_time=current_datetime,
            interchange_control_reference=interchange_ref
        )
        cuscar.add_segment(unb)
        
        # Add UNH segment (Message Header)
        unh = UNHSegment(message_reference=message_ref)
        cuscar.add_segment(unh)
        
        # Add BGM segment (Beginning of Message)
        bgm = BGMSegment(
            document_name_code="785",  # Cargo declaration
            document_number=request.reference_number,
            message_function=request.message_function.value
        )
        cuscar.add_segment(bgm)
        
        # Add RFF segment (Reference)
        rff = RFFSegment(
            reference_qualifier="VON",  # Voyage number
            reference_number=request.vessel.voyage_number
        )
        cuscar.add_segment(rff)
        
        # Add document references
        for doc_ref in request.document_references:
            rff = RFFSegment(
                reference_qualifier=doc_ref.document_type,
                reference_number=doc_ref.document_number
            )
            cuscar.add_segment(rff)
        
        # Add DTM segments (Date/Time)
        # Departure date
        departure_datetime = f"{request.departure_date.date}{request.departure_date.time or '0000'}"
        dtm_departure = DTMSegment(
            date_time_period=departure_datetime,
            qualifier="186"  # Departure date/time
        )
        cuscar.add_segment(dtm_departure)
        
        # Arrival date
        arrival_datetime = f"{request.arrival_date.date}{request.arrival_date.time or '0000'}"
        dtm_arrival = DTMSegment(
            date_time_period=arrival_datetime,
            qualifier="132"  # Arrival date/time
        )
        cuscar.add_segment(dtm_arrival)
        
        # Add TDT segment (Transport Information)
        tdt = TDTSegment(
            transport_stage_code="20",  # Main carriage
            mode_of_transport="1",      # Maritime transport
            transport_identification=request.vessel.vessel_imo,
            transport_name=request.vessel.vessel_name
        )
        cuscar.add_segment(tdt)
        
        # Add LOC segments (Location)
        # Port of loading
        loc_loading = LOCSegment(
            location_qualifier="9",  # Place/port of loading
            location_code=request.loading_port.port_code,
            location_name=request.loading_port.port_name
        )
        cuscar.add_segment(loc_loading)
        
        # Port of discharge
        loc_discharge = LOCSegment(
            location_qualifier="11",  # Place/port of discharge
            location_code=request.discharge_port.port_code,
            location_name=request.discharge_port.port_name
        )
        cuscar.add_segment(loc_discharge)
        
        # Add NAD segments (Name and Address)
        # Carrier
        nad_carrier = NADSegment(
            party_qualifier="CA",  # Carrier
            party_id=request.carrier.party_identifier,
            party_name=request.carrier.party_name
        )
        cuscar.add_segment(nad_carrier)
        
        # Consignor (if provided)
        if request.consignor:
            nad_consignor = NADSegment(
                party_qualifier="CZ",  # Consignor
                party_id=request.consignor.party_identifier,
                party_name=request.consignor.party_name
            )
            cuscar.add_segment(nad_consignor)
        
        # Consignee (if provided)
        if request.consignee:
            nad_consignee = NADSegment(
                party_qualifier="CN",  # Consignee
                party_id=request.consignee.party_identifier,
                party_name=request.consignee.party_name
            )
            cuscar.add_segment(nad_consignee)
        
        # Process each container load
        for container_load in request.container_loads:
            container = container_load.container
            
            # Add EQD segment (Equipment Details)
            eqd = EQDSegment(
                equipment_qualifier="CN",  # Container
                equipment_id=container.container_number,
                equipment_size_type=container.container_type,
                equipment_status="5" if container.empty_indicator else "2"  # 5=Empty, 2=Loaded
            )
            cuscar.add_segment(eqd)
            
            # Add SEL segments (Seal Numbers)
            for seal_number in container.seal_numbers:
                sel = SELSegment(seal_number=seal_number)
                cuscar.add_segment(sel)
            
            # Process goods items in this container
            for goods_item in container_load.goods_items:
                # Add GID segment (Goods Item Details)
                gid = GIDSegment(goods_item_number=str(goods_item.item_number))
                cuscar.add_segment(gid)
                
                # Add FTX segment for goods description
                ftx = FTXSegment(
                    text_subject_code="AAA",  # Goods description
                    text=goods_item.description
                )
                cuscar.add_segment(ftx)
                
                # Add MEA segments for measurements
                # Gross weight
                mea_gross = MEASegment(
                    measurement_purpose="WT",  # Weight
                    measurement_dimension="AAB",  # Gross weight
                    measurement_value=str(goods_item.gross_weight),
                    unit_code="KGM"  # Kilogram
                )
                cuscar.add_segment(mea_gross)
                
                # Net weight (if provided)
                if goods_item.net_weight:
                    mea_net = MEASegment(
                        measurement_purpose="WT",  # Weight
                        measurement_dimension="AAL",  # Net weight
                        measurement_value=str(goods_item.net_weight),
                        unit_code="KGM"  # Kilogram
                    )
                    cuscar.add_segment(mea_net)
                
                # Volume (if provided)
                if goods_item.volume:
                    mea_volume = MEASegment(
                        measurement_purpose="VOL",  # Volume
                        measurement_dimension="AAW",  # Gross volume
                        measurement_value=str(goods_item.volume),
                        unit_code="MTQ"  # Cubic meter
                    )
                    cuscar.add_segment(mea_volume)
                
                # Add SGP segment to link goods item to container
                sgp = SGPSegment(
                    equipment_id=container.container_number,
                    package_count=str(goods_item.packages)
                )
                cuscar.add_segment(sgp)
        
        # Add UNT segment (Message Trailer)
        # Count segments (including UNT)
        segment_count = len(cuscar.segments) + 1
        unt = UNTSegment(
            segment_count=segment_count,
            message_reference=message_ref
        )
        cuscar.add_segment(unt)
        
        # Add UNZ segment (Interchange Trailer)
        unz = UNZSegment(
            message_count=1,
            interchange_control_reference=interchange_ref
        )
        cuscar.add_segment(unz)
        
        # Return the formatted message
        return str(cuscar)
        
    except Exception as e:
        logger.error(f"Error generating CUSCAR message: {str(e)}")
        raise
