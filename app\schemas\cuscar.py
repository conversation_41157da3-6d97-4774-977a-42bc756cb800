from pydantic import BaseModel, Field, field_validator
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class MessageFunction(str, Enum):
    ORIGINAL = "9"
    CHANGE = "5"
    REPLACEMENT = "1"

class VesselInformation(BaseModel):
    vessel_name: str = Field(..., description="Name of the vessel")
    vessel_imo: str = Field(..., description="IMO number of the vessel")
    call_sign: Optional[str] = Field(None, description="Call sign of the vessel")
    voyage_number: str = Field(..., description="Voyage number")
    carrier_code: Optional[str] = Field(None, description="Carrier code")

class PortInformation(BaseModel):
    port_code: str = Field(..., description="UN/LOCODE port code")
    port_name: Optional[str] = Field(None, description="Name of the port")
    terminal: Optional[str] = Field(None, description="Terminal code or name")

    @field_validator('port_code')
    def validate_port_code(cls, v):
        if len(v) != 5:
            raise ValueError("Port code must be a 5-character UN/LOCODE")
        return v

class DateInformation(BaseModel):
    date: str = Field(..., description="Date in YYYYMMDD format")
    time: Optional[str] = Field(None, description="Time in HHMM format")

    @field_validator('date')
    def validate_date(cls, v):
        if len(v) != 8:
            raise ValueError("Date must be in YYYYMMDD format")
        try:
            datetime.strptime(v, "%Y%m%d")
        except ValueError:
            raise ValueError("Invalid date format, must be YYYYMMDD")
        return v

    @field_validator('time')
    def validate_time(cls, v):
        if v is not None and len(v) != 4:
            raise ValueError("Time must be in HHMM format")
        if v is not None:
            try:
                datetime.strptime(v, "%H%M")
            except ValueError:
                raise ValueError("Invalid time format, must be HHMM")
        return v

class PartyInformation(BaseModel):
    party_name: str = Field(..., description="Name of the party")
    party_identifier: Optional[str] = Field(None, description="Identifier of the party")
    party_address: Optional[str] = Field(None, description="Address of the party")
    party_city: Optional[str] = Field(None, description="City of the party")
    party_country: Optional[str] = Field(None, description="Country code of the party")
    party_contact: Optional[str] = Field(None, description="Contact information")
    party_function: str = Field(..., description="Function code of the party (e.g., 'CZ' for consignor)")

class ContainerInformation(BaseModel):
    container_number: str = Field(..., description="Container number")
    container_type: Optional[str] = Field(None, description="Container type code")
    seal_numbers: List[str] = Field(default=[], description="List of seal numbers")
    empty_indicator: bool = Field(False, description="Indicator if container is empty")

    @field_validator('container_number')
    def validate_container_number(cls, v):
        if len(v) != 11:
            raise ValueError("Container number must be 11 characters")
        return v

class GoodsItem(BaseModel):
    item_number: int = Field(..., description="Item number")
    description: str = Field(..., description="Description of goods")
    packages: int = Field(..., description="Number of packages")
    package_type: str = Field(..., description="Package type code")
    gross_weight: float = Field(..., description="Gross weight in KG")
    net_weight: Optional[float] = Field(None, description="Net weight in KG")
    volume: Optional[float] = Field(None, description="Volume in CBM")
    dangerous_goods_code: Optional[str] = Field(None, description="IMDG code if applicable")
    hs_code: Optional[str] = Field(None, description="Harmonized System code")

    @field_validator('item_number')
    def validate_item_number(cls, v):
        if v < 1:
            raise ValueError("Item number must be positive")
        return v

class ContainerLoad(BaseModel):
    container: ContainerInformation
    goods_items: List[GoodsItem] = Field(..., description="Goods items in this container")

class DocumentReference(BaseModel):
    document_type: str = Field(..., description="Document type code")
    document_number: str = Field(..., description="Document number")
    document_date: Optional[DateInformation] = Field(None, description="Document date")

class CuscarRequest(BaseModel):
    message_function: MessageFunction = Field(..., description="Function of the message (original, change, replacement)")
    reference_number: str = Field(..., description="Reference number for this message")
    vessel: VesselInformation = Field(..., description="Vessel information")
    loading_port: PortInformation = Field(..., description="Port of loading")
    discharge_port: PortInformation = Field(..., description="Port of discharge")
    departure_date: DateInformation = Field(..., description="Departure date and time")
    arrival_date: DateInformation = Field(..., description="Arrival date and time")
    carrier: PartyInformation = Field(..., description="Carrier information")
    consignor: Optional[PartyInformation] = Field(None, description="Consignor information")
    consignee: Optional[PartyInformation] = Field(None, description="Consignee information")
    container_loads: List[ContainerLoad] = Field(..., description="Container loads with goods items")
    document_references: List[DocumentReference] = Field(default=[], description="Document references")

class CuscarResponse(BaseModel):
    message: str = Field(..., description="Generated EDIFACT CUSCAR message")
    message_type: str = Field("CUSCAR", description="Message type")
    edifact_version: str = Field("D96B", description="EDIFACT version")
    status: str = Field("success", description="Status of the operation")

class ErrorResponse(BaseModel):
    detail: str = Field(..., description="Error details")
    status: str = Field("error", description="Status of the operation")
