# CUSCAR API Documentation

## Overview

The CUSCAR Message Generator API provides endpoints for generating UN/EDIFACT CUSCAR (Customs Cargo Report) messages from cargo manifest data.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

Currently, the API does not require authentication.

## Endpoints

### Generate CUSCAR Message

```
POST /cuscar
```

Generates a CUSCAR message from the provided cargo manifest data.

#### Request Body

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| message_function | string | Function of the message (9=original, 5=change, 1=replacement) | Yes |
| reference_number | string | Reference number for this message | Yes |
| vessel | object | Vessel information | Yes |
| loading_port | object | Port of loading | Yes |
| discharge_port | object | Port of discharge | Yes |
| departure_date | object | Departure date and time | Yes |
| arrival_date | object | Arrival date and time | Yes |
| carrier | object | Carrier information | Yes |
| consignor | object | Consignor information | No |
| consignee | object | Consignee information | No |
| container_loads | array | Container loads with goods items | Yes |
| document_references | array | Document references | No |

#### Vessel Information

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| vessel_name | string | Name of the vessel | Yes |
| vessel_imo | string | IMO number of the vessel | Yes |
| call_sign | string | Call sign of the vessel | No |
| voyage_number | string | Voyage number | Yes |
| carrier_code | string | Carrier code | No |

#### Port Information

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| port_code | string | UN/LOCODE port code (5 characters) | Yes |
| port_name | string | Name of the port | No |
| terminal | string | Terminal code or name | No |

#### Date Information

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| date | string | Date in YYYYMMDD format | Yes |
| time | string | Time in HHMM format | No |

#### Party Information

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| party_name | string | Name of the party | Yes |
| party_identifier | string | Identifier of the party | No |
| party_address | string | Address of the party | No |
| party_city | string | City of the party | No |
| party_country | string | Country code of the party | No |
| party_contact | string | Contact information | No |
| party_function | string | Function code of the party | Yes |

#### Container Information

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| container_number | string | Container number (11 characters) | Yes |
| container_type | string | Container type code | No |
| seal_numbers | array | List of seal numbers | No |
| empty_indicator | boolean | Indicator if container is empty | No |

#### Goods Item

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| item_number | integer | Item number | Yes |
| description | string | Description of goods | Yes |
| packages | integer | Number of packages | Yes |
| package_type | string | Package type code | Yes |
| gross_weight | number | Gross weight in KG | Yes |
| net_weight | number | Net weight in KG | No |
| volume | number | Volume in CBM | No |
| dangerous_goods_code | string | IMDG code if applicable | No |
| hs_code | string | Harmonized System code | No |

#### Container Load

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| container | object | Container information | Yes |
| goods_items | array | Goods items in this container | Yes |

#### Document Reference

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| document_type | string | Document type code | Yes |
| document_number | string | Document number | Yes |
| document_date | object | Document date | No |

#### Example Request

```json
{
  "message_function": "9",
  "reference_number": "REF12345",
  "vessel": {
    "vessel_name": "TEST VESSEL",
    "vessel_imo": "IMO1234567",
    "call_sign": "ABCD",
    "voyage_number": "VOY123"
  },
  "loading_port": {
    "port_code": "USNYC",
    "port_name": "New York"
  },
  "discharge_port": {
    "port_code": "NLRTM",
    "port_name": "Rotterdam"
  },
  "departure_date": {
    "date": "20250101",
    "time": "1200"
  },
  "arrival_date": {
    "date": "20250115",
    "time": "1400"
  },
  "carrier": {
    "party_name": "TEST CARRIER",
    "party_identifier": "CARRIER123",
    "party_function": "CA"
  },
  "container_loads": [
    {
      "container": {
        "container_number": "ABCU1234567",
        "container_type": "22G1",
        "seal_numbers": ["SEAL001", "SEAL002"]
      },
      "goods_items": [
        {
          "item_number": 1,
          "description": "Test Goods",
          "packages": 10,
          "package_type": "PK",
          "gross_weight": 1000.0,
          "net_weight": 950.0,
          "volume": 20.0
        }
      ]
    }
  ],
  "document_references": [
    {
      "document_type": "BL",
      "document_number": "BL12345"
    }
  ]
}
```

#### Response

| Field | Type | Description |
|-------|------|-------------|
| message | string | Generated EDIFACT CUSCAR message |
| message_type | string | Message type (CUSCAR) |
| edifact_version | string | EDIFACT version (D96B) |
| status | string | Status of the operation (success) |

#### Example Response

```json
{
  "message": "UNA:+.? '\nUNB+UNOA:2+SENDER+RECIPIENT+230415:1200+CUS2304151200'\nUNH+MSG123456+CUSCAR:D:96B:UN'\nBGM+785+REF12345+9'\nRFF+VON:VOY123'\n...",
  "message_type": "CUSCAR",
  "edifact_version": "D96B",
  "status": "success"
}
```

#### Error Responses

##### 400 Bad Request

```json
{
  "detail": "Validation error: Port code must be a 5-character UN/LOCODE",
  "status": "error"
}
```

##### 422 Unprocessable Entity

```json
{
  "detail": [
    {
      "loc": ["body", "vessel", "vessel_name"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

##### 500 Internal Server Error

```json
{
  "detail": "Error generating CUSCAR message",
  "status": "error"
}
```

### Health Check

```
GET /health
```

Returns the health status of the application.

#### Response

```json
{
  "status": "healthy"
}
```

## Error Codes

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 400 | Bad Request - Invalid input data |
| 422 | Unprocessable Entity - Validation error |
| 500 | Internal Server Error - Server-side error |

## Rate Limiting

Currently, there are no rate limits implemented.
