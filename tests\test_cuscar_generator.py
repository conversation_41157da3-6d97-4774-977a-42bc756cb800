import pytest
from app.schemas.cuscar import (
    CuscarRequest, VesselInformation, PortInformation, DateInformation,
    PartyInformation, ContainerInformation, GoodsItem, ContainerLoad,
    DocumentReference, MessageFunction
)
from app.core.cuscar_generator import generate_cuscar_message

def create_sample_request():
    """Create a sample CUSCAR request for testing"""
    return CuscarRequest(
        message_function=MessageFunction.ORIGINAL,
        reference_number="REF12345",
        vessel=VesselInformation(
            vessel_name="TEST VESSEL",
            vessel_imo="IMO1234567",
            call_sign="ABCD",
            voyage_number="VOY123",
            carrier_code="CARR"
        ),
        loading_port=PortInformation(
            port_code="USNYC",
            port_name="New York"
        ),
        discharge_port=PortInformation(
            port_code="NLRTM",
            port_name="Rotterdam"
        ),
        departure_date=DateInformation(
            date="20250101",
            time="1200"
        ),
        arrival_date=DateInformation(
            date="20250115",
            time="1400"
        ),
        carrier=PartyInformation(
            party_name="TEST CARRIER",
            party_identifier="CARRIER123",
            party_function="CA"
        ),
        container_loads=[
            ContainerLoad(
                container=ContainerInformation(
                    container_number="ABCU1234567",
                    container_type="22G1",
                    seal_numbers=["SEAL001", "SEAL002"]
                ),
                goods_items=[
                    GoodsItem(
                        item_number=1,
                        description="Test Goods",
                        packages=10,
                        package_type="PK",
                        gross_weight=1000.0,
                        net_weight=950.0,
                        volume=20.0
                    )
                ]
            )
        ],
        document_references=[
            DocumentReference(
                document_type="BL",
                document_number="BL12345"
            )
        ]
    )

def test_generate_cuscar_message():
    """Test CUSCAR message generation"""
    # Create a sample request
    request = create_sample_request()

    # Generate the CUSCAR message
    cuscar_message = generate_cuscar_message(request)

    # Basic validation of the generated message
    assert cuscar_message is not None
    assert len(cuscar_message) > 0
    assert "UNA:+.? '" in cuscar_message
    assert "UNB+" in cuscar_message
    assert "UNH+" in cuscar_message
    assert "BGM+785+REF12345+9" in cuscar_message
    assert "TDT+20+1++:IMO1234567+TEST VESSEL" in cuscar_message
    assert "LOC+9+USNYC:6+New York" in cuscar_message
    assert "LOC+11+NLRTM:6+Rotterdam" in cuscar_message
    assert "EQD+CN+ABCU1234567+22G1+2" in cuscar_message
    assert "UNT+" in cuscar_message
    assert "UNZ+1+" in cuscar_message
