# Product Requirements Document: CUSCAR Service

## Overview
This document outlines the requirements for the CUSCAR (Customs Cargo Report) microservice as part of the EDI Services Platform. The service will process UN/EDIFACT CUSCAR messages according to SARS (South African Revenue Service) guidelines and standards.

## Requirements

### Functional Requirements
1. The service shall process CUSCAR message types according to UN/EDIFACT standards and SARS specifications
2. The service shall monitor a specified directory for incoming CUSCAR files
3. The service shall parse and validate CUSCAR messages
4. The service shall transform CUSCAR data into structured format (JSON)
5. The service shall provide a REST API for message submission and querying
6. The service shall implement proper error handling for malformed messages
7. The service shall track processing status for each file
8. The service shall store processed messages in a database
9. The service shall support cargo manifest reporting for sea, air, and land transport

### Non-Functional Requirements
1. The service shall be containerized using Docker
2. The service shall follow the microservice architecture pattern established in the platform
3. The service shall include comprehensive logging
4. The service shall be configurable via environment variables
5. The service shall include health check endpoints
6. The service shall handle files in a stable and idempotent manner
7. The service shall include appropriate documentation
8. The service shall include a test suite

### Technical Requirements
1. The service shall be implemented in Python
2. The service shall use FastAPI for the REST API
3. The service shall use SQLite for development and PostgreSQL for production
4. The service shall include a Dockerfile and docker-compose.yml
5. The service shall follow the established project structure of other services
6. The service shall expose port 8015 for API access

## CUSCAR-Specific Requirements
1. The service shall handle CUSCAR message segments including BGM, RFF, TDT, LOC, DTM, NAD, GID, PAC, PCI, and other segments specific to cargo reporting
2. The service shall extract and process cargo manifest information including:
   - Vessel/aircraft/vehicle details
   - Voyage/flight information
   - Bill of lading/airway bill details
   - Consignment information
   - Container details
   - Goods descriptions
   - Dangerous goods information
3. The service shall validate CUSCAR messages against SARS specifications
4. The service shall support the retrieval of cargo information by various identifiers (voyage number, bill of lading, etc.)
5. The service shall maintain a history of processed CUSCAR messages
6. The service shall support different message functions (original, amendment, cancellation)
7. The service shall handle both consolidated and house bill information

## API Endpoints
1. `GET /health`: Health check endpoint
2. `GET /api/v1/messages`: List all processed CUSCAR messages
3. `GET /api/v1/messages/{message_id}`: Get details of a specific CUSCAR message
4. `POST /api/v1/messages`: Submit a new CUSCAR message
5. `GET /api/v1/manifests`: List all cargo manifests
6. `GET /api/v1/manifests/{manifest_id}`: Get details of a specific manifest
7. `GET /api/v1/bills`: List all bills of lading/airway bills
8. `GET /api/v1/bills/{bill_id}`: Get details of a specific bill
9. `GET /api/v1/files`: List all processed files
10. `GET /api/v1/files/{filename}`: Get the content of a specific processed file

## Deliverables
1. Source code for the CUSCAR service
2. Dockerfile and docker-compose.yml
3. Documentation including README.md
4. Test suite with sample CUSCAR messages
5. Configuration examples (.env.example)

## Constraints
1. The service must integrate with the existing EDI Services Platform
2. The service must follow the established patterns and conventions
3. The service must be compatible with the existing infrastructure
4. The service must comply with SARS CUSCAR message specifications
