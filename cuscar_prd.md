# Product Requirements Document: SARS CUSCAR Message Generator

## 1. Executive Summary

### 1.1 Product Overview
The SARS CUSCAR (Customs Cargo Report) Message Generator is a microservice that generates UN/EDIFACT CUSCAR messages for South African Revenue Service (SARS) customs cargo reporting requirements. This service will complement the existing CUSDEC service by handling cargo manifest and arrival/departure notifications.

### 1.2 Business Justification
- **Regulatory Compliance**: SARS requires CUSCAR messages for cargo manifest reporting
- **Trade Facilitation**: Streamlines cargo clearance processes
- **Integration**: Complements existing CUSDEC declaration system
- **Automation**: Reduces manual cargo reporting overhead

## 2. Product Context

### 2.1 Relationship to CUSDEC
| Aspect | CUSDEC | CUSCAR |
|--------|--------|--------|
| **Purpose** | Customs Declaration | Cargo Report/Manifest |
| **Timing** | Pre-clearance | Pre-arrival/departure |
| **Content** | Detailed goods declaration | Cargo manifest summary |
| **Users** | Importers/Exporters/Agents | Carriers/Freight Forwarders |

### 2.2 Architecture Alignment
The CUSCAR service will follow the same architectural patterns as the CUSDEC service:
- FastAPI-based microservice
- Docker containerization
- SQLAlchemy ORM with SQLite/PostgreSQL
- EDIFACT message generation
- SARS-specific validation

## 3. Functional Requirements

### 3.1 Core Message Generation
**FR-001**: Generate UN/EDIFACT CUSCAR messages compliant with SARS specifications
- Support CUSCAR message version 30 (SARS standard)
- Generate proper UNA, UNB, UNH segments
- Include mandatory CUSCAR segments: BGM, DTM, LOC, TDT, NAD, GID, etc.

**FR-002**: Support multiple CUSCAR message functions
- Original cargo report (function code 9)
- Amendment (function code 5)
- Cancellation (function code 1)
- Replacement (function code 4)

### 3.2 SARS-Specific Requirements
**FR-003**: SARS Cargo Reporting Compliance
- Support SARS cargo manifest requirements
- Include vessel/flight/vehicle identification
- Support port/airport location codes
- Include cargo handling facility codes

**FR-004**: Transport Mode Support
- Maritime cargo (ships)
- Air cargo (aircraft)
- Road cargo (trucks)
- Rail cargo (trains)

### 3.3 Data Validation
**FR-005**: Input Data Validation
- Validate transport document numbers
- Verify SARS location codes
- Check cargo description formats
- Validate weight and quantity values

**FR-006**: SARS Business Rules
- Enforce SARS cargo reporting timelines
- Validate carrier registration numbers
- Check dangerous goods declarations
- Verify container/ULD identifications

## 4. Technical Requirements

### 4.1 Architecture (Based on CUSDEC Pattern)

```
cuscar-service/
├── CUSCAR.py                 # Main entry point
├── main.py                   # FastAPI application
├── config.py                 # Configuration management
├── models.py                 # Pydantic models
├── database.py               # Database models
├── run_cuscar.py            # Service launcher
├── api/
│   └── routes.py            # API endpoints
├── cuscar/
│   └── builder.py           # CUSCAR message builder
├── edifact/
│   └── core.py              # EDIFACT core components
├── validators/
│   └── cuscar_validator.py  # SARS CUSCAR validation
├── data/
│   ├── cuscar_sample.json   # Sample data
│   └── CUSCAR_SARS.pdf      # SARS specification
├── tests/
│   ├── test_cuscar.py       # Unit tests
│   └── test_sars_cuscar.py  # SARS integration tests
├── Dockerfile
├── docker-compose.yml
├── requirements.txt
└── .env
```

### 4.2 Data Models

#### 4.2.1 Core CUSCAR Models
```python
class CUSCARRequest(BaseModel):
    messageHeader: MessageHeader
    beginningOfMessage: BeginningOfMessage
    dateTimePeriod: List[DateTimePeriod]
    locations: List[Location]
    transportDetails: TransportDetails
    nameAndAddress: List[NameAndAddress]
    goodsItems: List[GoodsItem]
    equipmentDetails: Optional[List[EquipmentDetail]]
    dangerousGoods: Optional[List[DangerousGoods]]
```

#### 4.2.2 Transport-Specific Models
```python
class TransportDetails(BaseModel):
    transportMode: str  # 1=Maritime, 2=Rail, 3=Road, 4=Air
    transportMeans: str  # Vessel/Flight/Vehicle ID
    carrierCode: str     # SARS registered carrier
    voyageNumber: Optional[str]
    estimatedArrival: str
    estimatedDeparture: Optional[str]
```

#### 4.2.3 SARS-Specific Models
```python
class SARSCargoManifest(BaseModel):
    manifestNumber: str
    manifestType: str    # Import/Export/Transit
    portOfLoading: str   # SARS port code
    portOfDischarge: str # SARS port code
    customsOfficeCode: str
    carrierRegistration: str
    totalPackages: int
    totalGrossWeight: float
```

### 4.3 API Endpoints

#### 4.3.1 Core CUSCAR Operations
```
POST   /api/v1/cuscar              # Generate CUSCAR message
GET    /api/v1/cuscar              # List CUSCAR records
GET    /api/v1/cuscar/{id}         # Get specific CUSCAR record
PUT    /api/v1/cuscar/{id}         # Update CUSCAR record
DELETE /api/v1/cuscar/{id}         # Delete CUSCAR record
```

#### 4.3.2 SARS-Specific Endpoints
```
POST   /api/v1/cuscar/validate-sars    # Validate SARS CUSCAR
POST   /api/v1/cuscar/manifest         # Generate cargo manifest
GET    /api/v1/cuscar/transport-modes  # Get supported transport modes
GET    /api/v1/cuscar/location-codes   # Get SARS location codes
```

#### 4.3.3 Integration Endpoints
```
GET    /health                         # Health check
GET    /docs                          # API documentation
GET    /api/v1/cuscar/schema          # Message schema
```

### 4.4 Configuration (Following CUSDEC Pattern)

```python
class Config:
    # SARS CUSCAR Configuration
    EDIFACT_VERSION = "30"
    MESSAGE_TYPE = "CUSCAR"
    RECIPIENT_ID = "SARSCARGO"
    
    # SARS-specific settings
    SARS_CARGO_OFFICE = "JHB"
    SARS_CARRIER_CODE = ""
    SARS_MANIFEST_PREFIX = "MAN"
    
    # Transport mode codes
    TRANSPORT_MARITIME = "1"
    TRANSPORT_RAIL = "2"
    TRANSPORT_ROAD = "3"
    TRANSPORT_AIR = "4"
```

## 5. CUSCAR Message Structure

### 5.1 SARS CUSCAR Segments
```
UNA:+.? '
UNB+UNOB:4+SENDER+SARSCARGO+YYMMDD:HHMM+REF'
UNH+MSG001+CUSCAR:30:30:SARS'
BGM+85+MANIFEST001+9'                    # Cargo manifest
DTM+132:20240101:102'                    # Arrival date
DTM+133:20240102:102'                    # Departure date
LOC+5+ZADUR:139:6'                       # Port of loading
LOC+7+ZAJNB:139:6'                       # Port of discharge
TDT+20+VOYAGE001+1++CARRIER123:172'      # Transport details
NAD+CA+CARRIER123+++Carrier Name'        # Carrier
GID+1+10:CT'                            # Goods identification
FTX+AAA+++Cargo description'             # Free text
MEA+AAE++KGM:1000'                      # Measurements
EQD+CN+CONTAINER123+22G1'               # Equipment details
CNT+16:100'                             # Control totals
UNT+15+MSG001'
UNZ+1+REF'
```

### 5.2 Key CUSCAR vs CUSDEC Differences

| Segment | CUSDEC Usage | CUSCAR Usage |
|---------|--------------|--------------|
| **BGM** | Declaration (952) | Cargo Report (85) |
| **TDT** | Optional | Mandatory (transport details) |
| **GID** | Detailed goods | Summary cargo |
| **EQD** | Rare | Common (containers/ULDs) |
| **DGS** | Optional | Mandatory for dangerous goods |

## 6. Implementation Phases

### 6.1 Phase 1: Core Infrastructure (4 weeks)
- Set up project structure based on CUSDEC
- Implement basic FastAPI application
- Create database models and migrations
- Set up Docker containerization
- Implement basic EDIFACT core functionality

### 6.2 Phase 2: CUSCAR Message Generation (6 weeks)
- Implement CUSCAR builder following CUSDEC pattern
- Create Pydantic models for CUSCAR data
- Implement SARS-specific segment generation
- Add transport mode support
- Create basic validation

### 6.3 Phase 3: SARS Integration (4 weeks)
- Implement SARS CUSCAR validator
- Add SARS location code validation
- Implement carrier registration validation
- Add dangerous goods handling
- Create SARS-specific test cases

### 6.4 Phase 4: Advanced Features (3 weeks)
- Add equipment tracking (containers/ULDs)
- Implement cargo consolidation features
- Add manifest amendment capabilities
- Create reporting and analytics
- Performance optimization

### 6.5 Phase 5: Testing & Documentation (3 weeks)
- Comprehensive testing suite
- Integration testing with SARS systems
- API documentation
- Deployment guides
- User training materials

## 7. Success Criteria

### 7.1 Functional Success
- ✅ Generate valid SARS CUSCAR messages
- ✅ Support all major transport modes
- ✅ Pass SARS validation requirements
- ✅ Handle 1000+ messages per hour
- ✅ 99.9% uptime requirement

### 7.2 Technical Success
- ✅ Follow CUSDEC architectural patterns
- ✅ Maintain code quality standards
- ✅ Achieve 90%+ test coverage
- ✅ Sub-200ms response times
- ✅ Successful Docker deployment

### 7.3 Business Success
- ✅ Reduce cargo reporting time by 80%
- ✅ Achieve SARS compliance certification
- ✅ Support 50+ carriers/forwarders
- ✅ Process 10,000+ manifests monthly

## 8. Risk Assessment

### 8.1 Technical Risks
- **SARS Specification Changes**: Medium risk - Mitigate with flexible architecture
- **Transport Mode Complexity**: Medium risk - Implement incrementally
- **Performance Requirements**: Low risk - Based on proven CUSDEC architecture

### 8.2 Business Risks
- **SARS Approval Process**: High risk - Engage early with SARS stakeholders
- **Carrier Adoption**: Medium risk - Provide comprehensive training and support
- **Integration Complexity**: Low risk - Leverage existing CUSDEC patterns

## 9. Dependencies

### 9.1 Technical Dependencies
- CUSDEC service architecture and patterns
- SARS CUSCAR specification documents
- EDIFACT message standards
- Docker and containerization platform

### 9.2 Business Dependencies
- SARS approval and certification
- Carrier registration and onboarding
- Port/airport authority coordination
- Customs broker training

## 10. Success Metrics

### 10.1 Performance Metrics
- Message generation time: < 200ms
- Validation accuracy: > 99.5%
- System availability: > 99.9%
- Concurrent users: 100+

### 10.2 Business Metrics
- Cargo reporting compliance: 100%
- User adoption rate: 80% within 6 months
- Error reduction: 90% vs manual processes
- Processing time reduction: 80%

---

*This PRD provides a comprehensive blueprint for building a SARS CUSCAR message generator that leverages all the architectural patterns, best practices, and SARS-specific knowledge from the existing CUSDEC application.*
