import os
from pydantic_settings import BaseSettings
from typing import Optional, Dict, Any, List
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
load_dotenv()

class Settings(BaseSettings):
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "CUSCAR Message Generator"

    # EDIFACT Settings
    EDIFACT_VERSION: str = os.getenv("EDIFACT_VERSION", "D96B")
    UNA_SEGMENT: str = os.getenv("UNA_SEGMENT", "UNA:+.? '")

    # Service Configuration
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")

    # CORS Settings
    BACKEND_CORS_ORIGINS: List[str] = ["*"]

    class Config:
        case_sensitive = True
        env_file = ".env"

settings = Settings()
