from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class CuscarMessageBase(BaseModel):
    """Base schema for CUSCAR message"""
    reference_number: str = Field(..., description="Reference number for this message")
    message_type: str = Field("CUSCAR", description="Message type")
    edifact_version: str = Field("D96B", description="EDIFACT version")
    vessel_name: Optional[str] = Field(None, description="Name of the vessel")
    vessel_imo: Optional[str] = Field(None, description="IMO number of the vessel")
    voyage_number: Optional[str] = Field(None, description="Voyage number")
    loading_port: Optional[str] = Field(None, description="Port of loading")
    discharge_port: Optional[str] = Field(None, description="Port of discharge")
    departure_date: Optional[str] = Field(None, description="Departure date")
    arrival_date: Optional[str] = Field(None, description="Arrival date")

class CuscarMessageCreate(CuscarMessageBase):
    """Schema for creating a CUSCAR message"""
    message_content: str = Field(..., description="Generated EDIFACT CUSCAR message")

class CuscarMessageUpdate(BaseModel):
    """Schema for updating a CUSCAR message"""
    reference_number: Optional[str] = Field(None, description="Reference number for this message")
    message_content: Optional[str] = Field(None, description="Generated EDIFACT CUSCAR message")
    message_type: Optional[str] = Field(None, description="Message type")
    edifact_version: Optional[str] = Field(None, description="EDIFACT version")
    vessel_name: Optional[str] = Field(None, description="Name of the vessel")
    vessel_imo: Optional[str] = Field(None, description="IMO number of the vessel")
    voyage_number: Optional[str] = Field(None, description="Voyage number")
    loading_port: Optional[str] = Field(None, description="Port of loading")
    discharge_port: Optional[str] = Field(None, description="Port of discharge")
    departure_date: Optional[str] = Field(None, description="Departure date")
    arrival_date: Optional[str] = Field(None, description="Arrival date")
    is_active: Optional[bool] = Field(None, description="Whether the message is active")

class CuscarMessageInDB(CuscarMessageBase):
    """Schema for a CUSCAR message in the database"""
    id: int
    message_content: str
    created_at: datetime
    updated_at: datetime
    is_active: bool
    
    class Config:
        from_attributes = True

class CuscarMessageList(BaseModel):
    """Schema for a list of CUSCAR messages"""
    items: List[CuscarMessageInDB]
    total: int
