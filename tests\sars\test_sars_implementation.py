"""
Tests for SARS-specific implementation of CUSCAR service.
"""

import os
import json
import pytest
from unittest.mock import patch, MagicMock

from app.models.sars_cuscar import (
    SARSVessel, SARSPort, SARSDateTime, SARSParty,
    SARSContainer, SARSGoodsItem, SARSCuscarRequest
)
from app.core.sars_cuscar_generator import SARSCuscarGenerator, generate_sars_cuscar_message
from app.config.sars_config import sars_config


def load_test_data():
    """Load test data from the example file."""
    file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "examples", "sars_cuscar_request.json")
    with open(file_path, "r") as f:
        return json.load(f)


class TestSARSModels:
    """Tests for SARS-specific data models."""

    def test_sars_vessel_validation(self):
        """Test validation of SARS vessel model."""
        # Valid vessel
        vessel = SARSVessel(
            vessel_name="TEST VESSEL",
            vessel_imo="1234567",
            call_sign="ABCD",
            voyage_number="VOY123",
            vessel_nationality="ZA",
            vessel_type="1"
        )
        assert vessel.vessel_name == "TEST VESSEL"
        assert vessel.vessel_imo == "IMO1234567"  # IMO prefix added
        assert vessel.call_sign == "ABCD"
        assert vessel.voyage_number == "VOY123"
        assert vessel.vessel_nationality == "ZA"
        assert vessel.vessel_type == "1"

        # IMO number already has prefix
        vessel = SARSVessel(
            vessel_name="TEST VESSEL",
            vessel_imo="IMO1234567",
            call_sign="ABCD",
            voyage_number="VOY123"
        )
        assert vessel.vessel_imo == "IMO1234567"  # IMO prefix preserved

    def test_sars_port_validation(self):
        """Test validation of SARS port model."""
        # Valid port
        port = SARSPort(
            port_code="ZADUR",
            port_name="Durban",
            port_function="5"
        )
        assert port.port_code == "ZADUR"
        assert port.port_name == "Durban"
        assert port.port_function == "5"

    def test_sars_date_time_validation(self):
        """Test validation of SARS date/time model."""
        # Valid date/time
        date_time = SARSDateTime(
            date="20230501",
            time="1200",
            date_time_qualifier="133"
        )
        assert date_time.date == "20230501"
        assert date_time.time == "1200"
        assert date_time.date_time_qualifier == "133"

        # Invalid date format
        with pytest.raises(ValueError):
            SARSDateTime(
                date="2023-05-01",  # Wrong format
                time="1200",
                date_time_qualifier="133"
            )

    def test_sars_party_validation(self):
        """Test validation of SARS party model."""
        # Valid party
        party = SARSParty(
            party_name="TEST CARRIER",
            party_identifier="CARRIER123",
            party_function="CA",
            tin_number="1234567890",
            branch_code="001",
            customs_code="CUST12345"
        )
        assert party.party_name == "TEST CARRIER"
        assert party.party_identifier == "CARRIER123"
        assert party.party_function == "CA"
        assert party.tin_number == "1234567890"
        assert party.branch_code == "001"
        assert party.customs_code == "CUST12345"

        # Invalid TIN number
        with pytest.raises(ValueError):
            SARSParty(
                party_name="TEST CARRIER",
                party_identifier="CARRIER123",
                party_function="CA",
                tin_number="12345",  # Too short
                branch_code="001",
                customs_code="CUST12345"
            )

        # Invalid branch code
        with pytest.raises(ValueError):
            SARSParty(
                party_name="TEST CARRIER",
                party_identifier="CARRIER123",
                party_function="CA",
                tin_number="1234567890",
                branch_code="1",  # Too short
                customs_code="CUST12345"
            )

        # Invalid customs code
        with pytest.raises(ValueError):
            SARSParty(
                party_name="TEST CARRIER",
                party_identifier="CARRIER123",
                party_function="CA",
                tin_number="1234567890",
                branch_code="001",
                customs_code="C"  # Too short
            )

    def test_sars_container_validation(self):
        """Test validation of SARS container model."""
        # Valid container
        container = SARSContainer(
            container_number="ABCU1234567",
            container_type="22G1",
            seal_numbers=["SEAL001", "SEAL002"],
            container_status="5",
            empty_indicator="5"
        )
        assert container.container_number == "ABCU1234567"
        assert container.container_type == "22G1"
        assert container.seal_numbers == ["SEAL001", "SEAL002"]
        assert container.container_status == "5"
        assert container.empty_indicator == "5"

        # Invalid container number (too short)
        with pytest.raises(ValueError):
            SARSContainer(
                container_number="ABCU123",  # Too short
                container_type="22G1",
                seal_numbers=["SEAL001", "SEAL002"]
            )

        # Invalid container number (wrong format)
        with pytest.raises(ValueError):
            SARSContainer(
                container_number="1234567890A",  # Wrong format
                container_type="22G1",
                seal_numbers=["SEAL001", "SEAL002"]
            )

    def test_sars_goods_item_validation(self):
        """Test validation of SARS goods item model."""
        # Valid goods item
        goods_item = SARSGoodsItem(
            item_number=1,
            description="Test Goods",
            packages=10,
            package_type="PK",
            gross_weight=1000.0,
            net_weight=950.0,
            volume=20.0,
            customs_value=5000.0,
            tariff_code="84713000",
            origin_country="ZA"
        )
        assert goods_item.item_number == 1
        assert goods_item.description == "Test Goods"
        assert goods_item.packages == 10
        assert goods_item.package_type == "PK"
        assert goods_item.gross_weight == 1000.0
        assert goods_item.net_weight == 950.0
        assert goods_item.volume == 20.0
        assert goods_item.customs_value == 5000.0
        assert goods_item.tariff_code == "84713000"
        assert goods_item.origin_country == "ZA"

        # Invalid tariff code
        with pytest.raises(ValueError):
            SARSGoodsItem(
                item_number=1,
                description="Test Goods",
                packages=10,
                package_type="PK",
                gross_weight=1000.0,
                net_weight=950.0,
                volume=20.0,
                customs_value=5000.0,
                tariff_code="8471",  # Too short
                origin_country="ZA"
            )

        # Invalid origin country
        with pytest.raises(ValueError):
            SARSGoodsItem(
                item_number=1,
                description="Test Goods",
                packages=10,
                package_type="PK",
                gross_weight=1000.0,
                net_weight=950.0,
                volume=20.0,
                customs_value=5000.0,
                tariff_code="84713000",
                origin_country="ZAF"  # Too long
            )

    def test_sars_cuscar_request_validation(self):
        """Test validation of SARS CUSCAR request model."""
        # Load test data
        test_data = load_test_data()
        
        # Valid request
        request = SARSCuscarRequest(**test_data)
        assert request.message_function == "9"
        assert request.reference_number == "SARS20230501001"
        assert request.vessel.vessel_name == "TEST VESSEL"
        assert request.loading_port.port_code == "ZADUR"
        assert request.discharge_port.port_code == "ZAJNB"
        assert request.carrier.party_name == "TEST CARRIER"
        assert request.transport_mode == "1"
        
        # Invalid message function
        test_data_invalid = test_data.copy()
        test_data_invalid["message_function"] = "X"  # Invalid
        with pytest.raises(ValueError):
            SARSCuscarRequest(**test_data_invalid)
        
        # Invalid transport mode
        test_data_invalid = test_data.copy()
        test_data_invalid["transport_mode"] = "X"  # Invalid
        with pytest.raises(ValueError):
            SARSCuscarRequest(**test_data_invalid)


class TestSARSCuscarGenerator:
    """Tests for SARS CUSCAR generator."""

    def test_generator_initialization(self):
        """Test initialization of SARS CUSCAR generator."""
        # Load test data
        test_data = load_test_data()
        
        # Initialize generator
        generator = SARSCuscarGenerator(test_data)
        
        # Check configuration
        assert generator.message.syntax_identifier == sars_config.syntax_identifier
        assert generator.message.syntax_version == sars_config.syntax_version
        assert generator.message.sender_id == sars_config.sender_id
        assert generator.message.recipient_id == sars_config.recipient_id

    @patch("app.core.sars_cuscar_generator.datetime")
    def test_build_message(self, mock_datetime):
        """Test building SARS CUSCAR message."""
        # Mock datetime
        mock_datetime.now.return_value.strftime.side_effect = lambda fmt: "20230501120000" if fmt == "%Y%m%d%H%M%S" else "230501:1200"
        
        # Load test data
        test_data = load_test_data()
        
        # Initialize generator
        generator = SARSCuscarGenerator(test_data)
        
        # Build message
        message = generator.build()
        
        # Check message content
        assert "UNB+UNOB:4+" in message
        assert "UNH+" in message
        assert "CUSCAR:D:95B:UN:ZZZ01" in message
        assert "BGM+785+SARS20230501001+9" in message
        assert "RFF+VON:VOY123" in message
        assert "TDT+20+VOY123+1" in message
        assert "DTM+133:202305011200:203" in message
        assert "DTM+132:202305151400:203" in message
        assert "LOC+5+ZADUR:139" in message
        assert "LOC+7+ZAJNB:139" in message
        assert "NAD+CA+CARRIER123::9+TEST CARRIER" in message
        assert "EQD+CN+ABCU1234567::ZZZ+22G1:102" in message
        assert "SEL+SEAL001+CA" in message
        assert "GID+1+10+PK:21" in message
        assert "FTX+AAA++++Test Goods" in message
        assert "MEA+WT+AAB+KGM:1000.0" in message
        assert "SGP+ABCU1234567::ZZZ+10" in message
        assert "LOC+27+ZA:162" in message
        assert "PIA+5+84713000:HS:23" in message
        assert "UNT+" in message
        assert "UNZ+1+" in message

    def test_generate_sars_cuscar_message(self):
        """Test generate_sars_cuscar_message function."""
        # Load test data
        test_data = load_test_data()
        
        # Generate message
        with patch("app.core.sars_cuscar_generator.SARSCuscarGenerator") as mock_generator:
            mock_instance = MagicMock()
            mock_instance.build.return_value = "TEST CUSCAR MESSAGE"
            mock_generator.return_value = mock_instance
            
            message = generate_sars_cuscar_message(test_data)
            
            # Check that the generator was called with the correct data
            mock_generator.assert_called_once_with(test_data)
            mock_instance.build.assert_called_once()
            
            # Check the returned message
            assert message == "TEST CUSCAR MESSAGE"
