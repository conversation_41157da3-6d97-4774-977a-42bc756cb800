2025-04-05T23:50:26.553294+0200 INFO Received CUSCAR generation request for vessel: TEST VESSEL
2025-04-05T23:50:26.553294+0200 INFO Successfully generated CUSCAR message for vessel: TEST VESSEL
2025-04-05T23:51:01.277442+0200 INFO Received CUSCAR generation request for vessel: TEST VESSEL
2025-04-05T23:51:01.278896+0200 INFO Successfully generated CUSCAR message for vessel: TEST VESSEL
2025-04-15T05:01:16.722069+0000 ERROR Validation error: 7 validation errors for SARSCuscarRequest
message_reference
  Field required [type=missing, input_value={'vessel_information': {'...al_date': '2025-04-20'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
message_date
  Field required [type=missing, input_value={'vessel_information': {'...al_date': '2025-04-20'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
document_number
  Field required [type=missing, input_value={'vessel_information': {'...al_date': '2025-04-20'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
sender_id
  Field required [type=missing, input_value={'vessel_information': {'...al_date': '2025-04-20'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
vessel
  Field required [type=missing, input_value={'vessel_information': {'...al_date': '2025-04-20'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
port_of_loading
  Field required [type=missing, input_value={'vessel_information': {'...al_date': '2025-04-20'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
port_of_discharge
  Field required [type=missing, input_value={'vessel_information': {'...al_date': '2025-04-20'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-04-15T05:03:21.180541+0000 ERROR Validation error: 8 validation errors for SARSCuscarRequest
message_reference
  Field required [type=missing, input_value={'message_function': '9',...igin_country': 'ZA'}]}]}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
message_date
  Field required [type=missing, input_value={'message_function': '9',...igin_country': 'ZA'}]}]}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
document_number
  Field required [type=missing, input_value={'message_function': '9',...igin_country': 'ZA'}]}]}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
sender_id
  Field required [type=missing, input_value={'message_function': '9',...igin_country': 'ZA'}]}]}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
vessel.vessel_id
  Field required [type=missing, input_value={'vessel_name': 'TEST VES...ZA', 'vessel_type': '1'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
vessel.carrier_code
  Field required [type=missing, input_value={'vessel_name': 'TEST VES...ZA', 'vessel_type': '1'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
port_of_loading
  Field required [type=missing, input_value={'message_function': '9',...igin_country': 'ZA'}]}]}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
port_of_discharge
  Field required [type=missing, input_value={'message_function': '9',...igin_country': 'ZA'}]}]}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-04-15T05:07:53.375463+0000 INFO Received SARS CUSCAR generation request for vessel: TEST VESSEL
2025-04-15T05:07:53.376246+0000 INFO Generating SARS CUSCAR message
2025-04-15T05:07:53.376807+0000 INFO SARS CUSCAR message generated successfully
2025-04-15T05:07:53.377244+0000 INFO Successfully generated SARS CUSCAR message for vessel: TEST VESSEL
